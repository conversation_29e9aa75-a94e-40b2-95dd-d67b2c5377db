from pydantic import Field
from typing import Optional, List, Literal

from models.base import CamelCaseModel, DetectedRespResult
from models.pdf_page import PdfPageType
from vision.core.data_types import Point, DetectionResult


class Coordinate(DetectionResult):
    """钻孔坐标模型"""
    point: Optional[Point] = Field(...,description="坐标对应的点")
    x_coordinate: float = Field(..., description = "钻孔X坐标")
    y_coordinate: float = Field(..., description = "钻孔Y坐标")
    is_with_lined: bool = Field(..., description="坐标和钻孔之间是否连线")

class DrillInfo(DetectionResult):
    """钻孔数据模型"""
    type: Optional[str] = Field(..., description="数据信息类型")
    drill_number: str = Field(...,description="钻孔号")
    drill_elevation: Optional[float] = Field(..., description = "钻孔高程")
    drill_depth: Optional[float] =Field(..., description="钻孔深度")
    water_level_elevation: Optional[float] = Field(..., description="稳定水位高程")

class Drill(DetectionResult):
    """钻孔模型"""
    type: str = Field(..., description="钻孔类型")
    center_point: Optional[Point] = Field(None, description="钻孔圆心坐标")
    real_coordinate: Optional[Point] = Field(None, description="真实坐标")
    binding_info: Optional[DrillInfo] = Field(None, description="绑定的钻孔信息")

class DrillSpacing(DetectionResult):
    """钻孔距离模型"""
    drill1: Drill = Field(..., description="第一个钻孔")
    drill2: Drill = Field(..., description="第二个钻孔")
    plane_spacing: Optional[float] = Field(..., description="平面图中两个钻孔之间的距离")

class DetectedPlainResult(DetectedRespResult):
    """检测的平面图数据"""
    detect_type: Literal["平面图"] = PdfPageType.FLOOR_PLAN.desc
    x_value_per_pixel: Optional[float] = Field(default=0, description="每像素对应的实际长度")
    y_value_per_pixel: Optional[float] = Field(default=0, description="每像素对应的实际高度")
    drills: List[Drill] = Field(default_factory=list, description="钻孔列表")
    coordinates: List[Coordinate] = Field(default_factory=list, description="坐标列表")
    drill_infos: List[DrillInfo] = Field(default_factory=list, description="钻孔数据列表")
    drill_spacings: List[DrillSpacing] = Field(default_factory=list, description="钻孔距离列表")


