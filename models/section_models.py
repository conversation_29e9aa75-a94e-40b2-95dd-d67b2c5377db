"""
剖面相关数据模型
包含剖面详细识别、区域检测等相关的数据模型
"""

from pydantic import Field
from typing import List, Optional, Literal
from models.base import CamelCaseModel, DetectedRespResult
from models.pdf_page import PdfPageType
from vision.core import DetectionResult


class Spacing(DetectionResult):
    """间距数据模型"""
    value: float = Field(default=0, description="间距值(m)")


class RulerItem(DetectionResult):
    value: float = Field(..., description="刻度值")
    y: float = Field(..., description="刻度Y坐标")


class SectionNumber(DetectionResult):
    """剖面编号数据模型"""
    pass


class SamplePoint(DetectionResult):
    """土样信息模型"""
    depth: float = Field(..., description="土样深度(m)")
    elevation: float = Field(..., description="土样高程(m)")
    sample_type: str = Field(..., description="土样类型")


class DepthElevationPair(DetectionResult):
    """深度-高程数据对模型"""
    value: str = Field(..., description="深度-高程数据对")
    depth: float = Field(..., description="深度值(m)")
    elevation: float = Field(..., description="高程值(m)")


class DrillHole(DetectionResult):
    """钻孔数据模型"""
    drill_name: str = Field(..., description="钻孔编号")
    surface_elevation: float = Field(..., description="孔口标高(m)")
    depth_elevation_pairs: List[DepthElevationPair] = Field(default_factory=list, description="深度-高程数据对列表")
    x_coordinate: Optional[float] = Field(None, description="钻孔X坐标（用于排序）")
    assigned_spacing: Optional[Spacing] = Field(default=None, description="分配的间距信息")
    is_terminal_hole: bool = Field(False, description="是否为终孔")
    samples: List[SamplePoint] = Field(default_factory=list, description="取土样列表")


class Ruler(DetectionResult):
    """标尺数据模型"""
    value_per_pixel: float = Field(..., description="每像素值对应的实际长度")
    ruler_items: List[RulerItem] = Field(default_factory=list, description="刻度列表")


class SectionDetail(DetectionResult):
    """剖面数据模型"""
    section_number: Optional[SectionNumber] = Field(..., description="检测到的剖面编号列表")
    ruler: Optional[Ruler] = Field(..., description="标尺信息")
    drill_holes: Optional[List[DrillHole]] = Field(default_factory=list, description="钻孔列表")
    spacings: Optional[List[Spacing]] = Field(default_factory=list, description="间距列表，每个间距包含value(数值)、original_text(原始文本格式)和confidence字段")

class SpacingWithDrillHole(DetectionResult):
    """间距钻孔数据模型"""
    section_spacing: Spacing = Field(..., description="间距信息")
    drill_hole1: DrillHole = Field(..., description="第一个钻孔信息")
    drill_hole2: DrillHole = Field(..., description="第二个钻孔信息")

class DetectedSectionResult(DetectedRespResult):
    """检测的剖面数据"""
    detect_type: Literal["剖面图"] = PdfPageType.SECTION.desc
    sections: List[SectionDetail] = Field(default_factory=list, description="剖面列表")
    spacing_with_drill_holes: List[SpacingWithDrillHole] = Field(default_factory=list, description="间距钻孔列表")

