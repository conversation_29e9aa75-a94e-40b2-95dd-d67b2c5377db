"""
分层信息审查服务
处理不同类型表格的钻孔数据提取和对比分析
"""

from collections import defaultdict
from typing import List, Dict, Any, Optional, Tuple
import re
from loguru import logger

from models.section_models import DetectedSectionResult, DrillHole
from models.sheet_models import DetectedTableResult
from vision.core.data_types import DetectionResult
from vision.ocr_engine.engine import OcrEngine


class DrillHoleData:
    """
    钻孔数据统一结构

    Attributes:
        drill_id: 钻孔编号
        source_type: 数据来源类型
        layer_level: 土层层级标识（如①、②、③或1、2、3）
        layer_index: 土层层级索引（从1开始的整数）
        depth: 层底深度
        elevation: 层底标高/高程
        additional_data: 附加数据
    """
    def __init__(self, drill_id: str, source_type: str, layer_level: str = None, layer_index: int = None):
        self.drill_id = drill_id
        self.source_type = source_type
        self.layer_level = layer_level  # 土层层级标识（如①、②、③）
        self.layer_index = layer_index  # 土层层级索引（1、2、3...）
        self.depth: Optional[float] = None
        self.elevation: Optional[float] = None
        self.additional_data: Dict[str, Any] = {}


class StratificationReviewResult:
    """分层信息审查结果"""
    def __init__(self):
        self.drill_holes: Dict[str, List[DrillHoleData]] = defaultdict(list)
        self.comparisons: List[Dict[str, Any]] = []
        self.inconsistencies: List[Dict[str, Any]] = []
        self.summary: Dict[str, Any] = {}


class StratificationReviewService:
    """分层信息审查服务类"""
    
    def __init__(self):
        self.ocr_engine = OcrEngine.get_instance()
        
    async def review_stratification_info(self, logic_result_map: Dict[str, List[Any]]) -> Dict[str, Any]:
        """
        审查分层信息主函数
        
        Args:
            logic_result_map: 按类型分组的逻辑结果
            
        Returns:
            Dict[str, Any]: 审查结果
        """
        logger.info("开始审查分层信息")
        
        review_result = StratificationReviewResult()
        
        # 1. 从剖面图数据中提取钻孔信息
        if "剖面图" in logic_result_map:
            self._extract_from_section_results(logic_result_map["剖面图"], review_result)
        
        # 2. 从表格数据中提取钻孔信息
        if "表格为主" in logic_result_map:
            await self._extract_from_table_results(logic_result_map["表格为主"], review_result)
        
        # 3. 数据对比分析
        self._perform_data_comparison(review_result)
        
        # 4. 生成审查报告
        self._generate_review_summary(review_result)
        
        logger.info(f"分层信息审查完成，共处理 {len(review_result.drill_holes)} 个钻孔")
        
        return {
            "drill_holes": {k: [vars(dh) for dh in v] for k, v in review_result.drill_holes.items()},
            "comparisons": review_result.comparisons,
            "inconsistencies": review_result.inconsistencies,
            "summary": review_result.summary
        }
    
    def _extract_from_section_results(self, section_results: List[DetectedSectionResult], 
                                    review_result: StratificationReviewResult) -> None:
        """从剖面图结果中提取钻孔数据"""
        logger.info(f"处理剖面图数据，共 {len(section_results)} 个结果")
        
        for result in section_results:
            if not hasattr(result, 'sections') or not result.sections:
                continue
                
            for section in result.sections:
                if not section.drill_holes:
                    continue
                    
                for drill_hole in section.drill_holes:
                    drill_data = DrillHoleData(drill_hole.drill_name, "剖面图")
                    drill_data.elevation = drill_hole.surface_elevation
                    
                    # 从深度-高程对中提取深度信息
                    if drill_hole.depth_elevation_pairs:
                        depths = [pair.depth for pair in drill_hole.depth_elevation_pairs]
                        drill_data.depth = max(depths) if depths else None
                    
                    drill_data.additional_data = {
                        "depth_elevation_pairs": [vars(pair) for pair in drill_hole.depth_elevation_pairs],
                        "samples": [vars(sample) for sample in drill_hole.samples]
                    }
                    
                    review_result.drill_holes[drill_hole.drill_name].append(drill_data)
    
    async def _extract_from_table_results(self, table_results: List[DetectedTableResult], 
                                        review_result: StratificationReviewResult) -> None:
        """从表格结果中提取钻孔数据"""
        logger.info(f"处理表格数据，共 {len(table_results)} 个结果")
        
        for result in table_results:
            if not hasattr(result, 'vision_result') or not result.vision_result:
                continue
            
            # 识别表格类型并提取数据
            table_type = await self._identify_table_type(result.vision_result)
            
            if table_type == "钻孔柱状图":
                await self._extract_drill_bar_chart_data(result.vision_result, review_result)
            elif table_type == "静力触探测试成果图表":
                await self._extract_static_penetration_chart_data(result.vision_result, review_result)
            elif table_type == "静力触探分层参数表":
                await self._extract_static_penetration_param_data(result.vision_result, review_result)
            else:
                logger.warning(f"未识别的表格类型: {table_type}")
    
    async def _identify_table_type(self, vision_results: List[DetectionResult]) -> str:
        """识别表格类型"""
        # 通过OCR文本内容识别表格类型
        all_texts = []
        for detection in vision_results:
            if hasattr(detection, 'original_text') and detection.original_text:
                all_texts.append(detection.original_text.lower())
        
        combined_text = " ".join(all_texts)
        
        # 钻孔柱状图特征：包含"钻孔柱状图"和"土层层号"
        if "钻孔柱状图" in combined_text and "土层层号" in combined_text:
            return "钻孔柱状图"
        
        # 静力触探测试成果图表特征：包含"静力触探测试成果图表"和 "土层编号"
        if "静力触探测试成果图表" in combined_text and "土层编号：" in combined_text:
            return "静力触探测试成果图表"
        
        # 静力触探分层参数表特征：包含"土层编号"和"孔号："
        if "静力触探分层参数表" in combined_text and "孔号：" in combined_text:
            return "静力触探分层参数表"
        
        return "未知类型"

    async def _extract_drill_bar_chart_data(self, vision_results: List[DetectionResult],
                                          review_result: StratificationReviewResult) -> None:
        """提取钻孔柱状图数据"""
        logger.info("提取钻孔柱状图数据")

        # 1. 提取钻孔号（从标题"xxx钻孔柱状图"中）
        drill_id = self._extract_drill_id_from_title(vision_results, "钻孔柱状图")
        if not drill_id:
            logger.warning("未找到钻孔号")
            return

        # 2. 找到表头区域（"土层层号 层底深度 层底标高"）
        header_bbox = self._find_table_header(vision_results, ["土层层号", "层底深度", "层底标高"])
        if not header_bbox:
            logger.warning("未找到表头区域")
            return

        # 3. 根据表头位置提取数据行
        data_rows = self._extract_data_rows_below_header(vision_results, header_bbox)

        # 4. 解析数据并创建钻孔数据对象
        for row_data in data_rows:
            if len(row_data) >= 3:  # 至少包含土层层号、层底深度、层底标高
                try:
                    depth = float(row_data[1]) if row_data[1] else None
                    elevation = float(row_data[2]) if row_data[2] else None

                    drill_data = DrillHoleData(drill_id, "钻孔柱状图")
                    drill_data.depth = depth
                    drill_data.elevation = elevation
                    drill_data.additional_data = {
                        "soil_layer_number": row_data[0],
                        "raw_data": row_data
                    }

                    review_result.drill_holes[drill_id].append(drill_data)

                except (ValueError, TypeError) as e:
                    logger.warning(f"数据解析失败: {row_data}, 错误: {e}")

    async def _extract_static_penetration_chart_data(self, vision_results: List[DetectionResult],
                                                   review_result: StratificationReviewResult) -> None:
        """
        提取静力触探测试成果图表数据

        Args:
            vision_results: OCR识别结果列表，包含文本内容和bounding box信息
            review_result: 审查结果对象，用于存储提取的钻孔数据
        """
        logger.info("提取静力触探测试成果图表数据")

        # 1. 通过位置关系提取钻孔号（先找"孔号"文本，再找其右侧相邻的孔号值）
        drill_id = self._extract_drill_id_by_position(vision_results)
        if not drill_id:
            logger.warning("未找到钻孔号")
            return

        # 2. 找到表头区域（"土层编号 层底深度 层底标高"）
        header_bbox = self._find_table_header(vision_results, ["土层编号", "层底深度", "层底标高"])
        if not header_bbox:
            logger.warning("未找到表头区域")
            return

        # 3. 根据表头位置提取数据行
        data_rows = self._extract_data_rows_below_header(vision_results, header_bbox)

        # 4. 解析数据并创建钻孔数据对象
        for row_data in data_rows:
            if len(row_data) >= 3:
                try:
                    depth = float(row_data[1]) if row_data[1] else None
                    elevation = float(row_data[2]) if row_data[2] else None

                    drill_data = DrillHoleData(drill_id, "静力触探测试成果图表")
                    drill_data.depth = depth
                    drill_data.elevation = elevation
                    drill_data.additional_data = {
                        "soil_layer_code": row_data[0],
                        "raw_data": row_data
                    }

                    review_result.drill_holes[drill_id].append(drill_data)

                except (ValueError, TypeError) as e:
                    logger.warning(f"数据解析失败: {row_data}, 错误: {e}")

    async def _extract_static_penetration_param_data(self, vision_results: List[DetectionResult],
                                                   review_result: StratificationReviewResult) -> None:
        """提取静力触探分层参数表数据"""
        logger.info("提取静力触探分层参数表数据")

        # 1. 找到包含"孔号：xxx标高：xxx"的区域
        drill_info = self._extract_drill_info_from_header(vision_results)
        if not drill_info:
            logger.warning("未找到钻孔信息")
            return

        drill_id = drill_info.get("drill_id")
        surface_elevation = drill_info.get("elevation")

        # 2. 找到深度数据列（包含"0~1.70"等格式的数据）
        depth_ranges = self._extract_depth_ranges(vision_results)

        # 3. 创建钻孔数据对象
        for depth_range in depth_ranges:
            try:
                # 解析深度范围，如"1.70~3.50"
                start_depth, end_depth = self._parse_depth_range(depth_range)

                drill_data = DrillHoleData(drill_id, "静力触探分层参数表")
                drill_data.depth = end_depth  # 使用层底深度
                drill_data.elevation = surface_elevation - end_depth if surface_elevation else None
                drill_data.additional_data = {
                    "depth_range": depth_range,
                    "start_depth": start_depth,
                    "end_depth": end_depth,
                    "surface_elevation": surface_elevation
                }

                review_result.drill_holes[drill_id].append(drill_data)

            except (ValueError, TypeError) as e:
                logger.warning(f"深度范围解析失败: {depth_range}, 错误: {e}")

    def _extract_drill_id_from_title(self, vision_results: List[DetectionResult], title_suffix: str) -> Optional[str]:
        """从标题中提取钻孔号"""
        pattern = rf'(\w+){title_suffix}'

        for detection in vision_results:
            if hasattr(detection, 'original_text') and detection.original_text:
                match = re.search(pattern, detection.original_text)
                if match:
                    return match.group(1)

        return None

    def _extract_drill_id_from_content(self, vision_results: List[DetectionResult]) -> Optional[str]:
        """从内容中提取钻孔号"""
        drill_patterns = [
            r'ZK\d+',
            r'BH\d+',
            r'[A-Z]+\d+',
            r'孔号[：:]\s*(\w+)',
            r'钻孔[：:]\s*(\w+)'
        ]

        for detection in vision_results:
            if hasattr(detection, 'original_text') and detection.original_text:
                for pattern in drill_patterns:
                    match = re.search(pattern, detection.original_text, re.IGNORECASE)
                    if match:
                        return match.group(1) if match.groups() else match.group(0)

        return None

    def _extract_drill_id_by_position(self, vision_results: List[DetectionResult]) -> Optional[str]:
        """
        通过位置关系提取钻孔号
        先找到包含"孔号"文本的检测框，然后找到其右侧相邻的检测框作为孔号值

        Args:
            vision_results: OCR识别结果列表

        Returns:
            Optional[str]: 提取到的钻孔号，如果未找到则返回None
        """
        # 1. 找到包含"孔号"文本的检测框
        hole_label_detection = None
        for detection in vision_results:
            if (hasattr(detection, 'original_text') and detection.original_text and
                "孔号" in detection.original_text.replace(" ", "")):
                hole_label_detection = detection
                break

        if not hole_label_detection:
            logger.warning("未找到包含'孔号'文本的检测框")
            return None

        # 2. 找到其右侧相邻的检测框
        y_tolerance = 20  # Y坐标容差，用于判断是否在同一行
        min_x_distance = float('inf')
        drill_id_detection = None

        for detection in vision_results:
            if (detection != hole_label_detection and
                hasattr(detection, 'original_text') and detection.original_text and
                hasattr(detection, 'x1') and hasattr(detection, 'y1')):

                # 检查是否在同一行（Y坐标相近）
                y_diff = abs(detection.y1 - hole_label_detection.y1)
                if y_diff <= y_tolerance:
                    # 检查是否在右侧
                    x_distance = detection.x1 - hole_label_detection.x2
                    if x_distance > 0 and x_distance < min_x_distance:
                        min_x_distance = x_distance
                        drill_id_detection = detection

        if drill_id_detection and drill_id_detection.original_text:
            drill_id = drill_id_detection.original_text.strip()
            logger.info(f"通过位置关系找到钻孔号: {drill_id}")
            return drill_id

        logger.warning("未找到孔号右侧的相邻检测框")
        return None

    def _find_table_header(self, vision_results: List[DetectionResult],
                          header_keywords: List[str]) -> Optional[DetectionResult]:
        """
        找到包含指定关键词的表头区域

        Args:
            vision_results: OCR识别结果列表
            header_keywords: 表头关键词列表，如["土层编号", "层底深度", "层底标高"]

        Returns:
            Optional[DetectionResult]: 包含所有关键词的表头检测框，如果未找到则返回None
        """
        for detection in vision_results:
            if hasattr(detection, 'original_text') and detection.original_text:
                text = detection.original_text.replace(" ", "").replace("\n", "")
                if all(keyword in text for keyword in header_keywords):
                    return detection

        return None

    def _extract_data_rows_below_header(self, vision_results: List[DetectionResult],
                                      header_bbox: DetectionResult) -> List[List[str]]:
        """提取表头下方的数据行"""
        data_rows = []

        # 找到表头下方的所有文本框
        below_header_detections = []
        for detection in vision_results:
            if (hasattr(detection, 'y1') and hasattr(header_bbox, 'y2') and
                detection.y1 > header_bbox.y2):
                below_header_detections.append(detection)

        # 按Y坐标排序，然后按X坐标分组成行
        below_header_detections.sort(key=lambda d: (d.y1, d.x1))

        # 简单的行分组逻辑（基于Y坐标相近性）
        current_row = []
        current_y = None
        y_tolerance = 20  # Y坐标容差

        for detection in below_header_detections:
            if hasattr(detection, 'original_text') and detection.original_text:
                if current_y is None or abs(detection.y1 - current_y) <= y_tolerance:
                    current_row.append(detection.original_text.strip())
                    current_y = detection.y1
                else:
                    if current_row:
                        data_rows.append(current_row)
                    current_row = [detection.original_text.strip()]
                    current_y = detection.y1

        # 添加最后一行
        if current_row:
            data_rows.append(current_row)

        return data_rows

    def _extract_drill_info_from_header(self, vision_results: List[DetectionResult]) -> Optional[Dict[str, Any]]:
        """从表头提取钻孔信息（孔号和标高）"""
        pattern = r'孔号[：:]\s*(\w+).*?标高[：:]\s*([\d.]+)'

        for detection in vision_results:
            if hasattr(detection, 'original_text') and detection.original_text:
                match = re.search(pattern, detection.original_text)
                if match:
                    return {
                        "drill_id": match.group(1),
                        "elevation": float(match.group(2))
                    }

        return None

    def _extract_depth_ranges(self, vision_results: List[DetectionResult]) -> List[str]:
        """提取深度范围数据"""
        depth_ranges = []
        depth_pattern = r'\d+\.?\d*~\d+\.?\d*'

        for detection in vision_results:
            if hasattr(detection, 'original_text') and detection.original_text:
                matches = re.findall(depth_pattern, detection.original_text)
                depth_ranges.extend(matches)

        return depth_ranges

    def _parse_depth_range(self, depth_range: str) -> Tuple[float, float]:
        """解析深度范围字符串"""
        parts = depth_range.split('~')
        if len(parts) != 2:
            raise ValueError(f"无效的深度范围格式: {depth_range}")

        start_depth = float(parts[0])
        end_depth = float(parts[1])

        return start_depth, end_depth

    def _perform_data_comparison(self, review_result: StratificationReviewResult) -> None:
        """执行数据对比分析"""
        logger.info("开始数据对比分析")

        for drill_id, drill_data_list in review_result.drill_holes.items():
            if len(drill_data_list) < 2:
                continue  # 只有一个数据源，无法对比

            logger.debug(f"对比钻孔 {drill_id} 的数据，共 {len(drill_data_list)} 个数据源")

            comparison = {
                "drill_id": drill_id,
                "data_sources": [data.source_type for data in drill_data_list],
                "comparisons": [],
                "inconsistencies": []
            }

            # 对比深度数据
            depths = [(data.source_type, data.depth) for data in drill_data_list if data.depth is not None]
            if len(depths) > 1:
                depth_comparison = self._compare_depths(depths)
                comparison["comparisons"].append(depth_comparison)
                if not depth_comparison["is_consistent"]:
                    comparison["inconsistencies"].append(depth_comparison)

            # 对比标高数据
            elevations = [(data.source_type, data.elevation) for data in drill_data_list if data.elevation is not None]
            if len(elevations) > 1:
                elevation_comparison = self._compare_elevations(elevations)
                comparison["comparisons"].append(elevation_comparison)
                if not elevation_comparison["is_consistent"]:
                    comparison["inconsistencies"].append(elevation_comparison)

            review_result.comparisons.append(comparison)

            # 收集所有不一致项
            for inconsistency in comparison["inconsistencies"]:
                inconsistency["drill_id"] = drill_id
                review_result.inconsistencies.append(inconsistency)

        logger.info(f"数据对比完成，发现 {len(review_result.inconsistencies)} 个不一致项")

    def _compare_depths(self, depths: List[Tuple[str, float]]) -> Dict[str, Any]:
        """对比深度数据"""
        depth_values = [depth[1] for depth in depths if depth[1] is not None]

        if not depth_values:
            return {
                "type": "depth",
                "is_consistent": True,
                "message": "无有效深度数据"
            }

        max_diff = max(depth_values) - min(depth_values)
        tolerance = 0.2  # 20cm容差

        return {
            "type": "depth",
            "is_consistent": max_diff <= tolerance,
            "data": depths,
            "max_difference": max_diff,
            "tolerance": tolerance,
            "message": f"深度差异: {max_diff:.2f}m" + ("（在容差范围内）" if max_diff <= tolerance else "（超出容差）")
        }

    def _compare_elevations(self, elevations: List[Tuple[str, float]]) -> Dict[str, Any]:
        """对比标高数据"""
        elevation_values = [elev[1] for elev in elevations if elev[1] is not None]

        if not elevation_values:
            return {
                "type": "elevation",
                "is_consistent": True,
                "message": "无有效标高数据"
            }

        max_diff = max(elevation_values) - min(elevation_values)
        tolerance = 0.1  # 10cm容差

        return {
            "type": "elevation",
            "is_consistent": max_diff <= tolerance,
            "data": elevations,
            "max_difference": max_diff,
            "tolerance": tolerance,
            "message": f"标高差异: {max_diff:.2f}m" + ("（在容差范围内）" if max_diff <= tolerance else "（超出容差）")
        }

    def _generate_review_summary(self, review_result: StratificationReviewResult) -> None:
        """生成审查报告摘要"""
        logger.info("生成审查报告摘要")

        total_drill_holes = len(review_result.drill_holes)
        total_comparisons = len(review_result.comparisons)
        total_inconsistencies = len(review_result.inconsistencies)

        # 按数据源类型统计
        source_type_stats = defaultdict(int)
        for drill_data_list in review_result.drill_holes.values():
            for drill_data in drill_data_list:
                source_type_stats[drill_data.source_type] += 1

        # 按不一致类型统计
        inconsistency_type_stats = defaultdict(int)
        for inconsistency in review_result.inconsistencies:
            inconsistency_type_stats[inconsistency["type"]] += 1

        # 计算一致性率
        consistency_rate = ((total_comparisons - total_inconsistencies) / total_comparisons * 100) if total_comparisons > 0 else 100

        review_result.summary = {
            "total_drill_holes": total_drill_holes,
            "total_comparisons": total_comparisons,
            "total_inconsistencies": total_inconsistencies,
            "consistency_rate": round(consistency_rate, 2),
            "source_type_statistics": dict(source_type_stats),
            "inconsistency_type_statistics": dict(inconsistency_type_stats),
            "review_status": "完成",
            "recommendations": self._generate_recommendations(review_result)
        }

        logger.info(f"审查报告摘要生成完成，一致性率: {consistency_rate:.2f}%")

    def _generate_recommendations(self, review_result: StratificationReviewResult) -> List[str]:
        """生成改进建议"""
        recommendations = []

        if len(review_result.inconsistencies) == 0:
            recommendations.append("所有钻孔数据一致性良好，无需特别处理")
        else:
            if any(inc["type"] == "elevation" for inc in review_result.inconsistencies):
                recommendations.append("发现标高不一致，建议核实测量数据")

            if any(inc["type"] == "depth" for inc in review_result.inconsistencies):
                recommendations.append("发现深度数据不一致，建议检查钻探记录")

        # 数据源覆盖度建议
        single_source_holes = [drill_id for drill_id, data_list in review_result.drill_holes.items() if len(data_list) == 1]
        if single_source_holes:
            recommendations.append(f"有 {len(single_source_holes)} 个钻孔仅有单一数据源，建议增加交叉验证")

        return recommendations


# ==================== 测试相关代码 ====================

class StratificationTestData:
    """分层信息测试数据生成器"""

    @staticmethod
    def create_drill_bar_chart_test_data() -> List[DetectionResult]:
        """
        创建钻孔柱状图测试数据

        Returns:
            List[DetectionResult]: 模拟的OCR识别结果列表
        """
        test_data = [
            # 标题
            DetectionResult(x1=100, y1=50, x2=300, y2=80, original_text="ZK001钻孔柱状图"),
            # 表头
            DetectionResult(x1=50, y1=120, x2=400, y2=150, original_text="土层层号 层底深度 层底标高"),
            # 数据行1
            DetectionResult(x1=50, y1=160, x2=100, y2=180, original_text="①"),
            DetectionResult(x1=150, y1=160, x2=200, y2=180, original_text="1.5"),
            DetectionResult(x1=250, y1=160, x2=300, y2=180, original_text="13.5"),
            # 数据行2
            DetectionResult(x1=50, y1=190, x2=100, y2=210, original_text="②"),
            DetectionResult(x1=150, y1=190, x2=200, y2=210, original_text="2.8"),
            DetectionResult(x1=250, y1=190, x2=300, y2=210, original_text="12.2"),
            # 数据行3
            DetectionResult(x1=50, y1=220, x2=100, y2=240, original_text="③"),
            DetectionResult(x1=150, y1=220, x2=200, y2=240, original_text="4.2"),
            DetectionResult(x1=250, y1=220, x2=300, y2=240, original_text="10.8"),
        ]
        return test_data

    @staticmethod
    def create_static_penetration_chart_test_data() -> List[DetectionResult]:
        """
        创建静力触探测试成果图表测试数据

        Returns:
            List[DetectionResult]: 模拟的OCR识别结果列表
        """
        test_data = [
            # 孔号标签和值
            DetectionResult(x1=50, y1=50, x2=100, y2=70, original_text="孔号"),
            DetectionResult(x1=120, y1=50, x2=180, y2=70, original_text="ZK001"),
            # 表头
            DetectionResult(x1=50, y1=120, x2=400, y2=150, original_text="土层编号 层底深度 层底标高"),
            # 数据行1
            DetectionResult(x1=50, y1=160, x2=100, y2=180, original_text="①"),
            DetectionResult(x1=150, y1=160, x2=200, y2=180, original_text="1.5"),
            DetectionResult(x1=250, y1=160, x2=300, y2=180, original_text="13.5"),
            # 数据行2
            DetectionResult(x1=50, y1=190, x2=100, y2=210, original_text="②"),
            DetectionResult(x1=150, y1=190, x2=200, y2=210, original_text="2.8"),
            DetectionResult(x1=250, y1=190, x2=300, y2=210, original_text="12.2"),
        ]
        return test_data

    @staticmethod
    def create_static_penetration_param_test_data() -> List[DetectionResult]:
        """
        创建静力触探分层参数表测试数据

        Returns:
            List[DetectionResult]: 模拟的OCR识别结果列表
        """
        test_data = [
            # 表头第一行
            DetectionResult(x1=50, y1=50, x2=120, y2=80, original_text="土层编号"),
            DetectionResult(x1=150, y1=50, x2=350, y2=80, original_text="孔号：ZK001标高：15.0"),
            # 表头第二行
            DetectionResult(x1=150, y1=90, x2=200, y2=110, original_text="H"),
            # 数据行1
            DetectionResult(x1=50, y1=130, x2=120, y2=150, original_text="①"),
            DetectionResult(x1=150, y1=130, x2=250, y2=150, original_text="0~1.70"),
            # 数据行2
            DetectionResult(x1=50, y1=160, x2=120, y2=180, original_text="②"),
            DetectionResult(x1=150, y1=160, x2=250, y2=180, original_text="1.70~3.50"),
            # 数据行3
            DetectionResult(x1=50, y1=190, x2=120, y2=210, original_text="③"),
            DetectionResult(x1=150, y1=190, x2=250, y2=210, original_text="3.50~5.20"),
        ]
        return test_data


class StratificationTestVisualizer:
    """分层信息测试数据可视化器"""

    @staticmethod
    def visualize_test_data(test_data: List[DetectionResult], title: str,
                          output_path: str = None) -> str:
        """
        可视化测试数据的bounding box

        Args:
            test_data: 测试数据列表
            title: 图像标题
            output_path: 输出路径，如果为None则自动生成

        Returns:
            str: 保存的图像路径
        """
        import cv2
        import numpy as np
        from datetime import datetime

        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"stratification_test_{title}_{timestamp}.png"

        # 计算图像尺寸
        max_x = max(detection.x2 for detection in test_data) + 50
        max_y = max(detection.y2 for detection in test_data) + 50

        # 创建白色背景图像
        image = np.ones((max_y, max_x, 3), dtype=np.uint8) * 255

        # 定义不同类型检测框的颜色
        colors = {
            "title": (255, 0, 0),      # 红色 - 标题
            "header": (0, 255, 0),     # 绿色 - 表头
            "hole_label": (0, 0, 255), # 蓝色 - 孔号标签
            "hole_value": (255, 0, 255), # 紫色 - 孔号值
            "data": (0, 165, 255)      # 橙色 - 数据
        }

        # 绘制检测框和文本
        for detection in test_data:
            # 确定检测框类型
            text = detection.original_text if detection.original_text else ""
            if "钻孔柱状图" in text or "测试成果" in text:
                color = colors["title"]
                box_type = "标题"
            elif any(keyword in text for keyword in ["土层层号", "土层编号", "层底深度", "层底标高"]):
                color = colors["header"]
                box_type = "表头"
            elif "孔号" in text and len(text) <= 3:
                color = colors["hole_label"]
                box_type = "孔号标签"
            elif text.startswith("ZK") or text.startswith("BH"):
                color = colors["hole_value"]
                box_type = "孔号值"
            else:
                color = colors["data"]
                box_type = "数据"

            # 绘制矩形框
            cv2.rectangle(image, (detection.x1, detection.y1), (detection.x2, detection.y2), color, 2)

            # 绘制文本
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.6
            thickness = 1

            # 在框内绘制原始文本
            text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
            text_x = detection.x1 + 2
            text_y = detection.y1 + text_size[1] + 5
            cv2.putText(image, text, (text_x, text_y), font, font_scale, (0, 0, 0), thickness)

            # 在框上方绘制类型标签
            label_y = detection.y1 - 5
            cv2.putText(image, box_type, (detection.x1, label_y), font, 0.4, color, 1)

        # 添加标题
        title_font_scale = 1.0
        title_thickness = 2
        title_size = cv2.getTextSize(title, font, title_font_scale, title_thickness)[0]
        title_x = (max_x - title_size[0]) // 2
        cv2.putText(image, title, (title_x, 30), font, title_font_scale, (0, 0, 0), title_thickness)

        # 保存图像
        cv2.imwrite(output_path, image)
        logger.info(f"测试数据可视化图像已保存: {output_path}")

        return output_path


class StratificationTestRunner:
    """分层信息测试运行器"""

    def __init__(self):
        self.service = StratificationReviewService()
        self.visualizer = StratificationTestVisualizer()

    async def run_all_tests(self) -> Dict[str, Any]:
        """
        运行所有测试

        Returns:
            Dict[str, Any]: 测试结果汇总
        """
        logger.info("开始运行分层信息测试")

        test_results = {
            "drill_bar_chart": await self.test_drill_bar_chart_extraction(),
            "static_penetration_chart": await self.test_static_penetration_chart_extraction(),
            "static_penetration_param": await self.test_static_penetration_param_extraction(),
            "comparison_test": await self.test_data_comparison()
        }

        logger.info("所有测试完成")
        return test_results

    async def test_drill_bar_chart_extraction(self) -> Dict[str, Any]:
        """测试钻孔柱状图数据提取"""
        logger.info("测试钻孔柱状图数据提取")

        # 创建测试数据
        test_data = StratificationTestData.create_drill_bar_chart_test_data()

        # 可视化测试数据
        vis_path = self.visualizer.visualize_test_data(test_data, "钻孔柱状图测试数据")

        # 执行提取测试
        review_result = StratificationReviewResult()
        await self.service._extract_drill_bar_chart_data(test_data, review_result)

        # 验证结果
        success = len(review_result.drill_holes) > 0
        drill_data_list = list(review_result.drill_holes.values())[0] if success else []

        result = {
            "success": success,
            "drill_holes_count": len(review_result.drill_holes),
            "data_points_count": len(drill_data_list),
            "visualization_path": vis_path,
            "extracted_data": [vars(data) for data in drill_data_list] if drill_data_list else []
        }

        logger.info(f"钻孔柱状图测试结果: {result}")
        return result

    async def test_static_penetration_chart_extraction(self) -> Dict[str, Any]:
        """测试静力触探测试成果图表数据提取"""
        logger.info("测试静力触探测试成果图表数据提取")

        # 创建测试数据
        test_data = StratificationTestData.create_static_penetration_chart_test_data()

        # 可视化测试数据
        vis_path = self.visualizer.visualize_test_data(test_data, "静力触探测试成果图表测试数据")

        # 执行提取测试
        review_result = StratificationReviewResult()
        await self.service._extract_static_penetration_chart_data(test_data, review_result)

        # 验证结果
        success = len(review_result.drill_holes) > 0
        drill_data_list = list(review_result.drill_holes.values())[0] if success else []

        result = {
            "success": success,
            "drill_holes_count": len(review_result.drill_holes),
            "data_points_count": len(drill_data_list),
            "visualization_path": vis_path,
            "extracted_data": [vars(data) for data in drill_data_list] if drill_data_list else []
        }

        logger.info(f"静力触探测试成果图表测试结果: {result}")
        return result

    async def test_static_penetration_param_extraction(self) -> Dict[str, Any]:
        """测试静力触探分层参数表数据提取"""
        logger.info("测试静力触探分层参数表数据提取")

        # 创建测试数据
        test_data = StratificationTestData.create_static_penetration_param_test_data()

        # 可视化测试数据
        vis_path = self.visualizer.visualize_test_data(test_data, "静力触探分层参数表测试数据")

        # 执行提取测试
        review_result = StratificationReviewResult()
        await self.service._extract_static_penetration_param_data(test_data, review_result)

        # 验证结果
        success = len(review_result.drill_holes) > 0
        drill_data_list = list(review_result.drill_holes.values())[0] if success else []

        result = {
            "success": success,
            "drill_holes_count": len(review_result.drill_holes),
            "data_points_count": len(drill_data_list),
            "visualization_path": vis_path,
            "extracted_data": [vars(data) for data in drill_data_list] if drill_data_list else []
        }

        logger.info(f"静力触探分层参数表测试结果: {result}")
        return result

    async def test_data_comparison(self) -> Dict[str, Any]:
        """测试数据对比功能"""
        logger.info("测试数据对比功能")

        # 创建模拟的对比数据
        review_result = StratificationReviewResult()

        # 添加同一钻孔的不同数据源
        drill_data1 = DrillHoleData("ZK001", "钻孔柱状图")
        drill_data1.depth = 4.2
        drill_data1.elevation = 10.8

        drill_data2 = DrillHoleData("ZK001", "静力触探测试成果图表")
        drill_data2.depth = 4.3  # 略有差异
        drill_data2.elevation = 10.7  # 略有差异

        review_result.drill_holes["ZK001"].extend([drill_data1, drill_data2])

        # 执行对比
        self.service._perform_data_comparison(review_result)

        # 生成报告
        self.service._generate_review_summary(review_result)

        result = {
            "success": len(review_result.comparisons) > 0,
            "comparisons_count": len(review_result.comparisons),
            "inconsistencies_count": len(review_result.inconsistencies),
            "consistency_rate": review_result.summary.get("consistency_rate", 0),
            "summary": review_result.summary
        }

        logger.info(f"数据对比测试结果: {result}")
        return result


# ==================== 独立测试函数 ====================

async def run_stratification_tests():
    """
    运行分层信息测试的独立函数
    可以直接调用此函数进行测试
    """
    test_runner = StratificationTestRunner()
    results = await test_runner.run_all_tests()

    print("=" * 60)
    print("分层信息审查功能测试结果")
    print("=" * 60)

    for test_name, result in results.items():
        print(f"\n{test_name}:")
        print(f"  成功: {result['success']}")
        if 'visualization_path' in result:
            print(f"  可视化图片: {result['visualization_path']}")
        if 'drill_holes_count' in result:
            print(f"  钻孔数量: {result['drill_holes_count']}")
        if 'data_points_count' in result:
            print(f"  数据点数量: {result['data_points_count']}")
        if 'consistency_rate' in result:
            print(f"  一致性率: {result['consistency_rate']}%")

    return results


if __name__ == "__main__":
    import asyncio
    asyncio.run(run_stratification_tests())
