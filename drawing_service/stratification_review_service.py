"""
分层信息审查服务
支持按土层层级进行精确对比的地质钻探数据审查功能
"""

from collections import defaultdict
from typing import List, Dict, Any, Optional, Tuple
from models.section_models import DetectedSectionResult
from models.sheet_models import DetectedTableResult
from vision.core.data_types import DetectionResult
from loguru import logger
import re


class DrillHoleData:
    """
    钻孔数据统一结构
    
    Attributes:
        drill_id: 钻孔编号
        source_type: 数据来源类型
        layer_level: 土层层级标识（如①、②、③或1、2、3）
        layer_index: 土层层级索引（从1开始的整数）
        depth: 层底深度
        elevation: 层底标高/高程
        additional_data: 附加数据
    """
    def __init__(self, drill_id: str, source_type: str, layer_level: str = None, layer_index: int = None):
        self.drill_id = drill_id
        self.source_type = source_type
        self.layer_level = layer_level  # 土层层级标识（如①、②、③）
        self.layer_index = layer_index  # 土层层级索引（1、2、3...）
        self.depth: Optional[float] = None
        self.elevation: Optional[float] = None
        self.additional_data: Dict[str, Any] = {}


class StratificationReviewResult:
    """
    分层信息审查结果
    
    Attributes:
        drill_holes: 钻孔数据字典，key为钻孔ID，value为该钻孔的所有层级数据
        comparisons: 对比结果列表，每个元素包含钻孔ID、层级和对比详情
        inconsistencies: 不一致项列表，记录所有发现的数据不一致情况
        summary: 审查报告摘要，包含统计信息和建议
    """
    def __init__(self):
        self.drill_holes: Dict[str, List[DrillHoleData]] = defaultdict(list)
        self.comparisons: List[Dict[str, Any]] = []
        self.inconsistencies: List[Dict[str, Any]] = []
        self.summary: Dict[str, Any] = {}


class StratificationReviewService:
    """
    分层信息审查服务
    支持按土层层级进行精确对比
    """
    
    async def review_stratification_info(self, logic_result_map: Dict[str, List]) -> Dict[str, Any]:
        """
        审查分层信息
        按土层层级进行精确对比
        
        Args:
            logic_result_map: 逻辑结果映射，key为检测类型，value为结果列表
            
        Returns:
            Dict[str, Any]: 审查结果
        """
        logger.info("开始分层信息审查")
        
        review_result = StratificationReviewResult()
        
        # 1. 从剖面图结果中提取数据
        if "section" in logic_result_map:
            self._extract_from_section_results(logic_result_map["section"], review_result)
        
        # 2. 从表格结果中提取数据
        if "table" in logic_result_map:
            await self._extract_from_table_results(logic_result_map["table"], review_result)
        
        # 3. 执行数据对比分析
        self._perform_data_comparison(review_result)
        
        # 4. 生成审查报告摘要
        self._generate_review_summary(review_result)
        
        logger.info("分层信息审查完成")
        
        return {
            "drill_holes": {k: [vars(data) for data in v] for k, v in review_result.drill_holes.items()},
            "comparisons": review_result.comparisons,
            "inconsistencies": review_result.inconsistencies,
            "summary": review_result.summary
        }
    
    def _extract_from_section_results(self, section_results: List[DetectedSectionResult], 
                                    review_result: StratificationReviewResult) -> None:
        """
        从剖面图结果中提取钻孔数据
        利用depth_elevation_pairs数组索引作为层级顺序
        
        Args:
            section_results: 剖面图检测结果列表
            review_result: 审查结果对象，用于存储提取的数据
        """
        logger.info(f"处理剖面图数据，共 {len(section_results)} 个结果")
        
        for result in section_results:
            if not hasattr(result, 'sections') or not result.sections:
                continue
                
            for section in result.sections:
                if not section.drill_holes:
                    continue
                    
                for drill_hole in section.drill_holes:
                    # 从深度-高程对中按层级提取数据
                    if drill_hole.depth_elevation_pairs:
                        for layer_index, pair in enumerate(drill_hole.depth_elevation_pairs, 1):
                            # 为每个层级创建单独的钻孔数据对象
                            drill_data = DrillHoleData(
                                drill_hole.drill_name, 
                                "剖面图",
                                layer_level=str(layer_index),  # 使用数字作为层级标识
                                layer_index=layer_index
                            )
                            drill_data.depth = pair.depth
                            drill_data.elevation = pair.elevation
                            drill_data.additional_data = {
                                "pair_data": vars(pair),
                                "surface_elevation": drill_hole.surface_elevation,
                                "samples": [vars(sample) for sample in drill_hole.samples if hasattr(sample, 'depth') and abs(sample.depth - pair.depth) < 0.1]
                            }
                            
                            review_result.drill_holes[drill_hole.drill_name].append(drill_data)
                    else:
                        logger.warning(f"剖面图钻孔 {drill_hole.drill_name} 没有深度-高程对数据")
    
    async def _extract_from_table_results(self, table_results: List[DetectedTableResult], 
                                        review_result: StratificationReviewResult) -> None:
        """
        从表格结果中提取钻孔数据
        支持三种表格类型的数据提取
        
        Args:
            table_results: 表格检测结果列表
            review_result: 审查结果对象
        """
        logger.info(f"处理表格数据，共 {len(table_results)} 个结果")
        
        for table_result in table_results:
            if not hasattr(table_result, 'vision_result') or not table_result.vision_result:
                continue
            
            vision_results = table_result.vision_result
            
            # 识别表格类型并提取数据
            table_type = await self._identify_table_type(vision_results)
            
            if "钻孔柱状图" in table_type:
                await self._extract_drill_bar_chart_data(vision_results, review_result)
            elif "静力触探测试成果图表" in table_type:
                await self._extract_static_penetration_chart_data(vision_results, review_result)
            elif "静力触探分层参数表" in table_type:
                await self._extract_static_penetration_param_data(vision_results, review_result)
            else:
                logger.warning(f"未识别的表格类型: {table_type}")
    
    async def _identify_table_type(self, vision_results: List[DetectionResult]) -> str:
        """
        识别表格类型
        
        Args:
            vision_results: OCR识别结果列表
            
        Returns:
            str: 表格类型描述
        """
        all_text = " ".join([result.original_text for result in vision_results if result.original_text])
        
        if "钻孔柱状图" in all_text:
            return "钻孔柱状图"
        elif "静力触探" in all_text and ("测试成果" in all_text or "土层编号" in all_text):
            return "静力触探测试成果图表"
        elif "静力触探" in all_text and ("分层参数" in all_text or "孔号：" in all_text):
            return "静力触探分层参数表"
        else:
            return "未知表格类型"
    
    def _parse_layer_index(self, layer_level: str, fallback_index: int) -> int:
        """
        解析层级标识，转换为数字索引
        
        Args:
            layer_level: 层级标识（如①、②、③、1、2、3等）
            fallback_index: 备用索引，当无法解析时使用
            
        Returns:
            int: 层级索引（从1开始）
        """
        if not layer_level:
            return fallback_index
        
        # 处理中文数字符号
        chinese_numbers = {
            '①': 1, '②': 2, '③': 3, '④': 4, '⑤': 5, '⑥': 6, '⑦': 7, '⑧': 8, '⑨': 9, '⑩': 10,
            '⑪': 11, '⑫': 12, '⑬': 13, '⑭': 14, '⑮': 15, '⑯': 16, '⑰': 17, '⑱': 18, '⑲': 19, '⑳': 20
        }
        
        if layer_level in chinese_numbers:
            return chinese_numbers[layer_level]
        
        # 处理阿拉伯数字
        try:
            return int(layer_level)
        except ValueError:
            pass
        
        # 处理罗马数字
        roman_numbers = {
            'I': 1, 'II': 2, 'III': 3, 'IV': 4, 'V': 5, 'VI': 6, 'VII': 7, 'VIII': 8, 'IX': 9, 'X': 10
        }
        
        if layer_level.upper() in roman_numbers:
            return roman_numbers[layer_level.upper()]
        
        logger.warning(f"无法解析层级标识: {layer_level}, 使用备用索引: {fallback_index}")
        return fallback_index
    
    def _generate_layer_level(self, layer_index: int) -> str:
        """
        根据层级索引生成层级标识
        
        Args:
            layer_index: 层级索引（从1开始）
            
        Returns:
            str: 层级标识（使用中文数字符号）
        """
        chinese_symbols = ['①', '②', '③', '④', '⑤', '⑥', '⑦', '⑧', '⑨', '⑩',
                          '⑪', '⑫', '⑬', '⑭', '⑮', '⑯', '⑰', '⑱', '⑲', '⑳']
        
        if 1 <= layer_index <= len(chinese_symbols):
            return chinese_symbols[layer_index - 1]
        else:
            return str(layer_index)  # 超出范围时使用数字

    async def _extract_drill_bar_chart_data(self, vision_results: List[DetectionResult],
                                          review_result: StratificationReviewResult) -> None:
        """
        提取钻孔柱状图数据
        从表格中提取土层层号、层底深度、层底标高信息

        Args:
            vision_results: OCR识别结果列表
            review_result: 审查结果对象
        """
        logger.info("提取钻孔柱状图数据")

        # 1. 提取钻孔号（从标题"xxx钻孔柱状图"中）
        drill_id = self._extract_drill_id_from_title(vision_results, "钻孔柱状图")
        if not drill_id:
            logger.warning("未找到钻孔号")
            return

        # 2. 找到表头区域（"土层层号 层底深度 层底标高"）
        header_bbox = self._find_table_header(vision_results, ["土层层号", "层底深度", "层底标高"])
        if not header_bbox:
            logger.warning("未找到表头区域")
            return

        # 3. 根据表头位置提取数据行
        data_rows = self._extract_data_rows_below_header(vision_results, header_bbox)

        # 4. 解析数据并创建钻孔数据对象
        for row_index, row_data in enumerate(data_rows, 1):
            if len(row_data) >= 3:  # 至少包含土层层号、层底深度、层底标高
                try:
                    layer_level = row_data[0].strip()  # 土层层号（如①、②、③）
                    depth = float(row_data[1]) if row_data[1] else None
                    elevation = float(row_data[2]) if row_data[2] else None

                    # 提取层级索引（将①②③转换为1、2、3）
                    layer_index = self._parse_layer_index(layer_level, row_index)

                    drill_data = DrillHoleData(
                        drill_id,
                        "钻孔柱状图",
                        layer_level=layer_level,
                        layer_index=layer_index
                    )
                    drill_data.depth = depth
                    drill_data.elevation = elevation
                    drill_data.additional_data = {
                        "soil_layer_number": layer_level,
                        "raw_data": row_data
                    }

                    review_result.drill_holes[drill_id].append(drill_data)
                    logger.debug(f"提取钻孔柱状图数据: {drill_id} 第{layer_index}层 深度{depth} 标高{elevation}")

                except (ValueError, TypeError) as e:
                    logger.warning(f"钻孔柱状图数据解析失败: {row_data}, 错误: {e}")

    async def _extract_static_penetration_chart_data(self, vision_results: List[DetectionResult],
                                                   review_result: StratificationReviewResult) -> None:
        """
        提取静力触探测试成果图表数据
        通过位置关系提取孔号，从表格中提取土层编号、层底深度、层底标高

        Args:
            vision_results: OCR识别结果列表
            review_result: 审查结果对象
        """
        logger.info("提取静力触探测试成果图表数据")

        # 1. 通过位置关系提取钻孔号
        drill_id = self._extract_drill_id_by_position(vision_results)
        if not drill_id:
            logger.warning("未找到钻孔号")
            return

        # 2. 找到表头区域（"土层编号 层底深度 层底标高"）
        header_bbox = self._find_table_header(vision_results, ["土层编号", "层底深度", "层底标高"])
        if not header_bbox:
            logger.warning("未找到表头区域")
            return

        # 3. 根据表头位置提取数据行
        data_rows = self._extract_data_rows_below_header(vision_results, header_bbox)

        # 4. 解析数据并创建钻孔数据对象
        for row_index, row_data in enumerate(data_rows, 1):
            if len(row_data) >= 3:
                try:
                    layer_level = row_data[0].strip()  # 土层编号（如①、②、③）
                    depth = float(row_data[1]) if row_data[1] else None
                    elevation = float(row_data[2]) if row_data[2] else None

                    # 提取层级索引
                    layer_index = self._parse_layer_index(layer_level, row_index)

                    drill_data = DrillHoleData(
                        drill_id,
                        "静力触探测试成果图表",
                        layer_level=layer_level,
                        layer_index=layer_index
                    )
                    drill_data.depth = depth
                    drill_data.elevation = elevation
                    drill_data.additional_data = {
                        "soil_layer_code": layer_level,
                        "raw_data": row_data
                    }

                    review_result.drill_holes[drill_id].append(drill_data)
                    logger.debug(f"提取静力触探测试成果图表数据: {drill_id} 第{layer_index}层 深度{depth} 标高{elevation}")

                except (ValueError, TypeError) as e:
                    logger.warning(f"静力触探测试成果图表数据解析失败: {row_data}, 错误: {e}")

    async def _extract_static_penetration_param_data(self, vision_results: List[DetectionResult],
                                                   review_result: StratificationReviewResult) -> None:
        """
        提取静力触探分层参数表数据
        从特殊表头结构中提取孔号和标高，解析深度范围数据

        Args:
            vision_results: OCR识别结果列表
            review_result: 审查结果对象
        """
        logger.info("提取静力触探分层参数表数据")

        # 1. 从表头提取钻孔号和地面标高
        drill_id, surface_elevation = self._extract_drill_info_from_header(vision_results)
        if not drill_id:
            logger.warning("未找到钻孔号")
            return

        # 2. 提取深度范围数据
        depth_ranges = self._extract_depth_ranges(vision_results)
        if not depth_ranges:
            logger.warning("未找到深度范围数据")
            return

        # 3. 创建钻孔数据对象
        for layer_index, depth_range in enumerate(depth_ranges, 1):
            try:
                # 解析深度范围，如"1.70~3.50"
                start_depth, end_depth = self._parse_depth_range(depth_range)

                # 生成层级标识（使用中文数字或符号）
                layer_level = self._generate_layer_level(layer_index)

                drill_data = DrillHoleData(
                    drill_id,
                    "静力触探分层参数表",
                    layer_level=layer_level,
                    layer_index=layer_index
                )
                drill_data.depth = end_depth  # 使用层底深度
                drill_data.elevation = surface_elevation - end_depth if surface_elevation else None
                drill_data.additional_data = {
                    "depth_range": depth_range,
                    "start_depth": start_depth,
                    "end_depth": end_depth,
                    "surface_elevation": surface_elevation
                }

                review_result.drill_holes[drill_id].append(drill_data)
                logger.debug(f"提取静力触探分层参数表数据: {drill_id} 第{layer_index}层 深度{end_depth} 标高{drill_data.elevation}")

            except (ValueError, TypeError) as e:
                logger.warning(f"静力触探分层参数表深度范围解析失败: {depth_range}, 错误: {e}")

    def _perform_data_comparison(self, review_result: StratificationReviewResult) -> None:
        """
        执行数据对比分析
        按相同钻孔号和相同土层层级进行对比
        """
        logger.info("开始执行数据对比分析")

        for drill_id, drill_data_list in review_result.drill_holes.items():
            if len(drill_data_list) < 2:
                continue  # 需要至少两个数据源才能对比

            # 按层级索引分组
            layer_groups = defaultdict(list)
            for data in drill_data_list:
                if data.layer_index is not None:
                    layer_groups[data.layer_index].append(data)

            logger.debug(f"钻孔 {drill_id} 共有 {len(layer_groups)} 个层级需要对比")

            # 对比每个层级的数据
            for layer_index, layer_data_list in layer_groups.items():
                if len(layer_data_list) < 2:
                    continue  # 需要至少两个数据源才能对比

                # 按数据源分组
                source_groups = defaultdict(list)
                for data in layer_data_list:
                    source_groups[data.source_type].append(data)

                # 获取层级标识（使用第一个数据的layer_level）
                layer_level = layer_data_list[0].layer_level or str(layer_index)

                # 对比不同数据源的同层级数据
                source_types = list(source_groups.keys())
                for i in range(len(source_types)):
                    for j in range(i + 1, len(source_types)):
                        source1, source2 = source_types[i], source_types[j]
                        self._compare_layer_between_sources(
                            drill_id, layer_index, layer_level,
                            source1, source_groups[source1],
                            source2, source_groups[source2],
                            review_result
                        )

        logger.info(f"数据对比完成，发现 {len(review_result.inconsistencies)} 个不一致项")

    def _compare_layer_between_sources(self, drill_id: str, layer_index: int, layer_level: str,
                                     source1: str, data_list1: List[DrillHoleData],
                                     source2: str, data_list2: List[DrillHoleData],
                                     review_result: StratificationReviewResult) -> None:
        """
        对比两个数据源的同层级数据

        Args:
            drill_id: 钻孔编号
            layer_index: 层级索引
            layer_level: 层级标识
            source1: 第一个数据源类型
            data_list1: 第一个数据源的数据列表
            source2: 第二个数据源类型
            data_list2: 第二个数据源的数据列表
            review_result: 审查结果对象
        """
        # 取每个数据源的第一个数据（通常每个层级每个数据源只有一个数据点）
        data1 = data_list1[0] if data_list1 else None
        data2 = data_list2[0] if data_list2 else None

        if not data1 or not data2:
            return

        # 创建对比结果
        comparison = {
            "drill_id": drill_id,
            "layer_level": layer_level,
            "layer_index": layer_index,
            "source1": source1,
            "source2": source2,
            "depth_comparison": None,
            "elevation_comparison": None
        }

        # 对比深度数据
        if data1.depth is not None and data2.depth is not None:
            depth_consistent = data1.depth == data2.depth
            comparison["depth_comparison"] = {
                "source1_value": data1.depth,
                "source2_value": data2.depth,
                "is_consistent": depth_consistent,
                "difference": abs(data1.depth - data2.depth)
            }

            if not depth_consistent:
                inconsistency = {
                    "drill_id": drill_id,
                    "layer_level": layer_level,
                    "layer_index": layer_index,
                    "type": "depth",
                    "source1": source1,
                    "source2": source2,
                    "source1_value": data1.depth,
                    "source2_value": data2.depth,
                    "message": f"{source1}和{source2}的钻孔{drill_id}第{layer_index}层深度数据不一致"
                }
                review_result.inconsistencies.append(inconsistency)

        # 对比标高数据
        if data1.elevation is not None and data2.elevation is not None:
            elevation_consistent = data1.elevation == data2.elevation
            comparison["elevation_comparison"] = {
                "source1_value": data1.elevation,
                "source2_value": data2.elevation,
                "is_consistent": elevation_consistent,
                "difference": abs(data1.elevation - data2.elevation)
            }

            if not elevation_consistent:
                inconsistency = {
                    "drill_id": drill_id,
                    "layer_level": layer_level,
                    "layer_index": layer_index,
                    "type": "elevation",
                    "source1": source1,
                    "source2": source2,
                    "source1_value": data1.elevation,
                    "source2_value": data2.elevation,
                    "message": f"{source1}和{source2}的钻孔{drill_id}第{layer_index}层标高数据不一致"
                }
                review_result.inconsistencies.append(inconsistency)

        review_result.comparisons.append(comparison)
        logger.debug(f"完成钻孔{drill_id}第{layer_index}层数据对比: {source1} vs {source2}")

    def _generate_review_summary(self, review_result: StratificationReviewResult) -> None:
        """
        生成审查报告摘要
        按层级统计不一致情况
        """
        logger.info("生成审查报告摘要")

        total_drill_holes = len(review_result.drill_holes)
        total_comparisons = len(review_result.comparisons)
        total_inconsistencies = len(review_result.inconsistencies)

        # 按钻孔和层级统计不一致情况
        drill_hole_issues = defaultdict(list)
        layer_issues = defaultdict(int)  # 按层级统计不一致数量
        issue_types = defaultdict(int)   # 按问题类型统计

        for inconsistency in review_result.inconsistencies:
            drill_hole_issues[inconsistency["drill_id"]].append(inconsistency)
            layer_key = f"第{inconsistency['layer_index']}层"
            layer_issues[layer_key] += 1
            issue_types[inconsistency["type"]] += 1

        # 统计每个钻孔的层级数量
        drill_hole_layers = {}
        for drill_id, drill_data_list in review_result.drill_holes.items():
            unique_layers = set()
            for data in drill_data_list:
                if data.layer_index is not None:
                    unique_layers.add(data.layer_index)
            drill_hole_layers[drill_id] = len(unique_layers)

        # 按数据源类型统计
        source_type_stats = defaultdict(int)
        for drill_data_list in review_result.drill_holes.values():
            for drill_data in drill_data_list:
                source_type_stats[drill_data.source_type] += 1

        # 计算一致性率
        consistency_rate = ((total_comparisons - total_inconsistencies) / total_comparisons * 100) if total_comparisons > 0 else 100

        review_result.summary = {
            "total_drill_holes": total_drill_holes,
            "total_layers": sum(drill_hole_layers.values()),
            "total_comparisons": total_comparisons,
            "total_inconsistencies": total_inconsistencies,
            "consistency_rate": round(consistency_rate, 2),
            "drill_holes_with_issues": len(drill_hole_issues),
            "drill_hole_layers": drill_hole_layers,
            "layer_issues": dict(layer_issues),
            "issue_types": dict(issue_types),
            "source_type_statistics": dict(source_type_stats),
            "drill_hole_issues": dict(drill_hole_issues),
            "review_status": "完成",
            "recommendations": self._generate_recommendations(review_result)
        }

        logger.info(f"审查摘要: 共{total_drill_holes}个钻孔，{sum(drill_hole_layers.values())}个层级，"
                   f"发现{total_inconsistencies}个不一致项，一致性率{consistency_rate:.1f}%")
