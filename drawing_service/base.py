import math
import time
from abc import abstractmethod
from typing import List, Union, Tuple, Optional, Any, Dict, TypeVar

import cv2
from PIL import Image
import numpy as np
from loguru import logger

from models.base import VisionDetectedResult
from models.pdf_page import PdfPageType
from models.plane_models import Coordinate
from utils.base import format_elapsed
from utils.pdf import PdfTextItem
from vision.core import DetectionResult, ImageHandler
from vision.core.data_types import BoundingBox
from vision.core.utils import crop_bboxes, Visualizer
from vision.obj_det import Y<PERSON>OModelManager, YoloModelType
from vision.ocr_engine.base import OcrResult
from vision.ocr_engine.engine import OcrModelType, OcrEngine
from vision.ocr_engine.external.batch_ocr import BatchOcrProcessor
from vision.utils.grid_image_builder import ResizeStrategy

T = TypeVar('T', bound='BoundingBox')


class Detector:
    def __init__(
        self,
        ocr_model_name: OcrModelType = OcrModelType.RAPID,
        confidence_threshold: float = 0.5,
        iou_threshold: float = 0.45,
        tile_size: Tuple[int, int] = (640, 640),
        overlap_ratio: float = 0.2,
        batch_size: Optional[int] = None,
        merge_nms_threshold: float = 0.5,
        max_items_per_batch: int = 49,
        batch_ocr_gap: int = 50,
        batch_ocr_target_size: Tuple[int, int] = (640, 640),
        ocr_background_color: Tuple[int, int, int] = (255, 255, 255)
    ):
        self.ocr_model_name = ocr_model_name
        self.confidence_threshold = confidence_threshold
        self.iou_threshold = iou_threshold
        self.tile_size = tile_size
        self.overlap_ratio = overlap_ratio
        self.batch_size = batch_size
        self.merge_nms_threshold = merge_nms_threshold
        # 全局默认参数
        self.default_params: Dict[str, Any] = {
            "confidence_threshold": confidence_threshold,
            "iou_threshold": iou_threshold,
            "tile_size": tile_size,
            "overlap_ratio": overlap_ratio,
            "batch_size": batch_size,
            "merge_nms_threshold": merge_nms_threshold,
        }

        self.yolo_manager = YOLOModelManager()
        self.ocr_engine = OcrEngine.get_instance(model_name=ocr_model_name)
        self.batch_ocr_processor = BatchOcrProcessor.create_with_strategy(
            resize_strategy=ResizeStrategy.MAX_SIZE_CENTER,
            max_items_per_batch=49,
            size_tolerance=0.3  # 30% 的面积差异容忍度
        )

        self._detections: List[DetectionResult] = []
        self._ocr_result: Optional[OcrResult] = None
        self._roi_ocr_result: Optional[OcrResult] = None
        self._pdf_texts: Optional[List[PdfTextItem]] = None

    # ---------- 工具 ----------
    def _merge_params(self, **kwargs) -> Dict[str, Any]:
        """合并全局默认参数和方法参数（方法参数优先级更高）"""
        params = self.default_params.copy()
        for k, v in kwargs.items():
            if v is not None:  # 只有传入的才覆盖
                params[k] = v
        return params

    # ---------- 预测接口 ----------
    def add_large_image_predict(
        self,
        model_type: YoloModelType,
        image_input: Union[str, np.ndarray],
        *,
        confidence_threshold: Optional[float] = None,
        iou_threshold: Optional[float] = None,
        tile_size: Optional[Tuple[int, int]] = None,
        overlap_ratio: Optional[float] = None,
        batch_size: Optional[int] = None,
        merge_nms_threshold: Optional[float] = None,
    ) -> List[DetectionResult]:
        params = self._merge_params(
            confidence_threshold=confidence_threshold,
            iou_threshold=iou_threshold,
            tile_size=tile_size,
            overlap_ratio=overlap_ratio,
            batch_size=batch_size,
            merge_nms_threshold=merge_nms_threshold,
        )

        detector = self.yolo_manager.load_model(model_type, **params)
        results = detector.predict_large_image(
            image_input,
            params["tile_size"],
            params["overlap_ratio"],
            params["batch_size"],
            params["merge_nms_threshold"],
        )
        self._detections.extend(results)
        return results

    def add_image_predict(
        self,
        model_type: YoloModelType,
        image_input: Union[str, Any],
        *,
        confidence_threshold: Optional[float] = None,
        iou_threshold: Optional[float] = None,
        target_size: Optional[Tuple[int, int]] = None,
    ) -> List[DetectionResult]:
        params = self._merge_params(
            confidence_threshold=confidence_threshold,
            iou_threshold=iou_threshold,
            target_size=target_size,
        )

        detector = self.yolo_manager.load_model(model_type, **params)
        results = detector.predict_single(image_input)
        self._detections.extend(results)
        return results

    def add_ocr_recognize(
        self, image: Union[str, np.ndarray, Image.Image]
    ) -> OcrResult:
        ocr_result = self.ocr_engine.recognize(image)
        self._ocr_result = ocr_result
        return ocr_result

    # ---------- 结果访问 ----------
    @property
    def detections(self) -> List[DetectionResult]:
        return self._detections

    @property
    def ocr_result(self) -> Optional[OcrResult]:
        return self._ocr_result

    @property
    def roi_ocr_result(self) -> Optional[OcrResult]:
        return self._roi_ocr_result

    @property
    def pdf_texts(self) -> List[PdfTextItem]:
        return self._pdf_texts

    # ---------- DetectionResult 帮助方法 ----------
    def set_vision_result(self, vision_result: VisionDetectedResult):
        self._detections = vision_result.detected_results or []
        self._ocr_result = vision_result.ocr_result
        self._roi_ocr_result = vision_result.roi_ocr_result
        self._pdf_texts = vision_result.pdf_texts

    def filter_by_class(self, class_name: str) -> List[DetectionResult]:
        return [det for det in self._detections if det.class_name == class_name]

    def filter_in_class(self, *class_names: List[str]) -> List[DetectionResult]:
        return [det for det in self._detections if det.class_name in class_names]

    def filter_by_confidence(self, threshold: float) -> List[DetectionResult]:
        return [det for det in self._detections if det.confidence >= threshold]

    def get_class_counts(self) -> dict:
        counts = {}
        for det in self._detections:
            counts[det.class_name] = counts.get(det.class_name, 0) + 1
        return counts

    def get_average_confidence(self) -> float:
        if not self._detections:
            return 0.0
        return sum(det.confidence for det in self._detections) / len(self._detections)

    def clear_results(self):
        self._detections.clear()
        self._ocr_result = None


class VisionComputer(Detector):

    def __init__(
        self,
        page_type: PdfPageType,
        ocr_model_name: OcrModelType = OcrModelType.RAPID,
        confidence_threshold: float = 0.5,
        iou_threshold: float = 0.45,
        tile_size: Tuple[int, int] = (640, 640),
        overlap_ratio: float = 0.2,
        batch_size: Optional[int] = None,
        merge_nms_threshold: float = 0.5,
    ):
        super().__init__(
            ocr_model_name=ocr_model_name,
            confidence_threshold=confidence_threshold,
            iou_threshold=iou_threshold,
            tile_size=tile_size,
            overlap_ratio=overlap_ratio,
            batch_size=batch_size,
            merge_nms_threshold=merge_nms_threshold
        )
        self.page_type = page_type
        self.image = None

    def normalize_input(self, image_input):
        image, _ = ImageHandler.normalize_input(image_input)
        height, width = image.shape[:2]
        logger.info(f"开始检测{self.page_type.desc}，图像尺寸: {width}x{height}")
        return image, (width, height)

    def set_image_and_vision_result(self, image: np.ndarray, vision_result: VisionDetectedResult):
        self.image, _ = ImageHandler.normalize_input(image)
        self.set_vision_result(vision_result)

    def merge_ocr(self, ocr_result: OcrResult):
        if self._roi_ocr_result:
            self._roi_ocr_result = self._roi_ocr_result.merge(ocr_result)
        else:
            self._roi_ocr_result = ocr_result

    @abstractmethod
    def handle_vision_detection(self, image: np.ndarray):
        raise NotImplementedError()

    def handle_ocr_detection(self, image: np.ndarray):
        pass

    @abstractmethod
    def handle_logic_compute(self, image, vision_result: VisionDetectedResult) -> Any:
        raise NotImplementedError()

    def vision_detect(self, file_name: str, image_input: Union[str, np.ndarray]) -> VisionDetectedResult:
        self.image, (width, height) = self.normalize_input(image_input)
        logger.info(f"开始视觉检测: {file_name}, 类型: {self.page_type.desc}")
        t0 = time.time()
        self.handle_vision_detection(self.image)
        self.add_ocr_recognize(self.image)
        logger.info(f"完成视觉检测，耗时: {format_elapsed(time.time() - t0)}")
        return VisionDetectedResult(
            file_name=file_name,
            detect_type=self.page_type.desc,
            image_width=width,
            image_height=height,
            detected_results=self.detections,
            ocr_result=self.ocr_result,
        )

    def logic_compute(self, image, vision_result: VisionDetectedResult) -> Any:
        self.set_image_and_vision_result(image, vision_result)
        file_name = vision_result.file_name or "unknown"

        t0 = time.time()
        logger.info(f"开始 ROI 区域 OCR 识别, 文件名: {file_name}")
        self.handle_ocr_detection(self.image)
        logger.info(f"完成 ROI 区域 OCR 识别，耗时: {format_elapsed(time.time() - t0)}")

        logger.info(f"开始逻辑计算: {file_name}, 类型: {self.page_type.desc}")
        t1 = time.time()
        result = self.handle_logic_compute(image, vision_result)
        logger.info(f"完成逻辑计算，耗时: {format_elapsed(time.time() - t1)}")
        return result

    def detect(self, file_name: str, image_input: Union[str,Any]) -> Any:
        vision_detected_result = self.vision_detect(file_name, image_input)
        return self.logic_compute(self.image, vision_detected_result)

    def visualization(self, class_names: List[str] = None, show: bool = False, save_path: str = None) -> Optional[np.ndarray]:
        if self.image is None:
            logger.warning("图像未设置，无法可视化")
            return None

        detections = self.detections
        if class_names:
            detections = [det for det in self.detections if det.class_name in class_names]

        vis_image = Visualizer().draw_detections(self.image, detections)

        if show:
            cv2.namedWindow(f"{self.page_type.desc} Detection", cv2.WINDOW_NORMAL)
            cv2.imshow(f"{self.page_type.desc} Detection", vis_image)
            cv2.resizeWindow(f"{self.page_type.desc} Detection", 800, 600)
            cv2.waitKey(0)

        if save_path:
            cv2.imwrite(save_path, vis_image)
            logger.info(f"已保存可视化图像到: {save_path}")

        return vis_image

    def _extract_text_from_detection(self, image: np.ndarray, detection: DetectionResult) -> str:
        """文本提取"""
        try:
            rois, _ = crop_bboxes(image, [detection.xyxy])
            if not rois:
                return ""
            # OCR识别文本
            ocr_result = self.ocr_engine.recognize(rois[0])
            text = " ".join(ocr_result.text) if ocr_result.text else ""

            return text

        except Exception as e:
            logger.warning(f"提取文本失败: {e}")
            return ""


class DetectorHelper:

    @staticmethod
    def _filter_in_class(items: List[DetectionResult], *class_names: str) -> List[DetectionResult]:
        return [item for item in items if item.class_name in class_names]

    @staticmethod
    def _filter_by_class(items: List[DetectionResult], class_name: str) -> List[DetectionResult]:
        return [item for item in items if item.class_name == class_name]

    @staticmethod
    def _find_class_in_bbox(bbox: BoundingBox, items: List[DetectionResult], class_name=None) -> List[DetectionResult]:
        """
        在给定的边界框内查找指定类别的检测结果
        :param bbox: 边界框坐标
        :param items: 检测结果列表
        :param class_name: 指定的类别名称
        :return: 在边界框内的检测结果列表
        """
        found_items = DetectorHelper._find_item_in_bbox(bbox, items)
        if class_name:
            return [item for item in found_items if item.class_name == class_name]
        return found_items

    @staticmethod
    def _find_item_in_bbox(bbox: BoundingBox, items: List[T]) -> List[T]:
        """
        在给定的边界框内查找检测结果
        :param bbox: 边界框坐标
        :param items: 检测结果列表
        :return: 在边界框内的检测结果列表
        """
        found_items = []
        for item in items:
            if (item.x1 >= bbox.x1 and item.y1 >= bbox.y1 and
                    item.x2 <= bbox.x2 and item.y2 <= bbox.y2):
                found_items.append(item)

        return found_items

    @staticmethod
    def _find_class_in_x_range(x_min, x_max, items: List[DetectionResult], class_name=None, tolerance=0.0) -> List[DetectionResult]:
        """
        在给定的 x 范围内查找指定类别的检测结果
        :param x_min: 最小 x 坐标
        :param x_max: 最大 x 坐标
        :param items: 检测结果列表
        :param class_name: 指定的类别名称
        :param tolerance: 容差范围
        :return: 在 x 范围内的检测结果列表
        """
        found_items = DetectorHelper._find_item_in_x_range(x_min, x_max, items, tolerance)
        if class_name:
            return [item for item in found_items if item.class_name == class_name]
        return found_items

    @staticmethod
    def _find_item_in_x_range(x_min, x_max, items: List[T], tolerance=0.0) -> List[T]:
        """
        在给定的 x 范围内查找检测结果
        :param x_min: 最小 x 坐标
        :param x_max: 最大 x 坐标
        :param items: 检测结果列表
        :return: 在 x 范围内的检测结果列表
        """
        found_items = []
        for item in items:
            if item.x1 >= x_min - tolerance and item.x2 <= x_max + tolerance:
                found_items.append(item)

        return found_items

    @staticmethod
    def _find_class_center_in_y_range(y_min, y_max, items: List[DetectionResult], class_name=None, tolerance=0.0) -> List[DetectionResult]:
        """
        查找中心在给定的 y 范围内指定类别的检测结果
        :param y_min: 最小 y 坐标
        :param y_max: 最大 y 坐标
        :param items: 检测结果列表
        :param class_name: 指定的类别名称
        :param tolerance: 容差范围
        :return: 在 y 范围内的检测结果列表
        """
        found_items = []
        for item in items:
            y_center = (item.y1 + item.y2) / 2
            if y_min - tolerance <= y_center <= y_max + tolerance:
                found_items.append(item)
        if class_name:
            return [item for item in found_items if item.class_name == class_name]
        return found_items

    @staticmethod
    def _find_class_in_y_range(y_min, y_max, items: List[DetectionResult], class_name=None, tolerance=0.0) -> List[DetectionResult]:
        """
        在给定的 y 范围内查找指定类别的检测结果
        :param y_min: 最小 y 坐标
        :param y_max: 最大 y 坐标
        :param items: 检测结果列表
        :param class_name: 指定的类别名称
        :param tolerance: 容差范围
        :return: 在 y 范围内的检测结果列表
        """
        found_items = DetectorHelper._find_item_in_y_range(y_min, y_max, items, tolerance)
        if class_name:
            return [item for item in found_items if item.class_name == class_name]
        return found_items

    @staticmethod
    def _find_item_in_y_range(y_min, y_max, items: List[T], tolerance=0.0) -> List[T]:
        """
        在给定的 y 范围内查找检测结果
        :param y_min: 最小 y 坐标
        :param y_max: 最大 y 坐标
        :param items: 检测结果列表
        :return: 在 y 范围内的检测结果列表
        """
        found_items = []
        for item in items:
            if item.y1 >= y_min - tolerance and item.y2 <= y_max + tolerance:
                found_items.append(item)

        return found_items

    @staticmethod
    def is_horizontal_or_vertical(boxA: BoundingBox, boxB: BoundingBox) -> str:
        """
        判断两个 bbox 是水平排列还是垂直排列

        Returns:
            "horizontal": 如果两个框主要是左右关系（水平）
            "vertical": 如果两个框主要是上下关系（垂直）
        """
        # 获取中心点
        xA, yA, _, _ = boxA.xyxy
        xB, yB, _, _ = boxB.xyxy

        centerA_x = (boxA.x1 + boxA.x2) / 2
        centerA_y = (boxA.y1 + boxA.y2) / 2
        centerB_x = (boxB.x1 + boxB.x2) / 2
        centerB_y = (boxB.y1 + boxB.y2) / 2

        # 计算两个中心点的水平和垂直距离
        dx = abs(centerA_x - centerB_x)
        dy = abs(centerA_y - centerB_y)

        return "horizontal" if dx > dy else "vertical"

    @staticmethod
    def distance_calculator(x1, y1, x2, y2, ratio: float = 1.0):
        """
            计算两点之间的欧几里得距离

            参数：
            x1, y1 -- 第一个点的坐标
            x2, y2 -- 第二个点的坐标


            返回：
            两点之间的距离（浮点数）
            """
        # 计算坐标差值
        dx = x2 - x1
        dy = y2 - y1

        # 计算平方和并开平方
        distance = math.sqrt(dx ** 2 + dy ** 2)

        return distance / ratio

    @staticmethod
    def distance_ratio(coordinates: List[Coordinate]):
        """
        计算实际距离与图中距离的比值

        :return: (实际距离, 图中距离, 比值)
        """
        if len(coordinates) < 2:
            logger.warning("坐标列表长度小于2，无法计算距离比值")
            return None, None, 1.0
        real_point1 = (coordinates[0].x_coordinate, coordinates[0].y_coordinate)
        real_point2 = (coordinates[1].x_coordinate, coordinates[1].y_coordinate)
        img_point1 = (coordinates[0].point.x, coordinates[0].point.y)
        img_point2 = (coordinates[1].point.x, coordinates[1].point.y)
        # 实际距离
        real_dist = math.dist(real_point1, real_point2)
        # 图中距离
        img_dist = math.dist(img_point1, img_point2)

        if img_dist == 0:
            raise ValueError("图中两点重合，无法计算比值。")

        ratio = img_dist / real_dist
        return real_dist, img_dist, ratio


