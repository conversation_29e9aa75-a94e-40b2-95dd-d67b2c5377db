"""
分层信息审查服务
支持按土层层级进行精确对比的地质钻探数据审查功能
"""

from collections import defaultdict
from typing import List, Dict, Any, Optional, Tuple
from models.section_models import DetectedSectionResult
from models.sheet_models import DetectedTableResult
from vision.core.data_types import DetectionResult
from loguru import logger
import re


class DrillHoleData:
    """
    钻孔数据统一结构
    
    Attributes:
        drill_id: 钻孔编号
        source_type: 数据来源类型
        layer_level: 土层层级标识（如①、②、③或1、2、3）
        layer_index: 土层层级索引（从1开始的整数）
        depth: 层底深度
        elevation: 层底标高/高程
        additional_data: 附加数据
    """
    def __init__(self, drill_id: str, source_type: str, layer_level: str = None, layer_index: int = None):
        self.drill_id = drill_id
        self.source_type = source_type
        self.layer_level = layer_level  # 土层层级标识（如①、②、③）
        self.layer_index = layer_index  # 土层层级索引（1、2、3...）
        self.depth: Optional[float] = None
        self.elevation: Optional[float] = None
        self.additional_data: Dict[str, Any] = {}


class StratificationReviewResult:
    """
    分层信息审查结果
    
    Attributes:
        drill_holes: 钻孔数据字典，key为钻孔ID，value为该钻孔的所有层级数据
        comparisons: 对比结果列表，每个元素包含钻孔ID、层级和对比详情
        inconsistencies: 不一致项列表，记录所有发现的数据不一致情况
        summary: 审查报告摘要，包含统计信息和建议
    """
    def __init__(self):
        self.drill_holes: Dict[str, List[DrillHoleData]] = defaultdict(list)
        self.comparisons: List[Dict[str, Any]] = []
        self.inconsistencies: List[Dict[str, Any]] = []
        self.summary: Dict[str, Any] = {}


class StratificationReviewService:
    """
    分层信息审查服务
    支持按土层层级进行精确对比
    """
    
    async def review_stratification_info(self, logic_result_map: Dict[str, List]) -> Dict[str, Any]:
        """
        审查分层信息
        按土层层级进行精确对比
        
        Args:
            logic_result_map: 逻辑结果映射，key为检测类型，value为结果列表
            
        Returns:
            Dict[str, Any]: 审查结果
        """
        logger.info("开始分层信息审查")
        
        review_result = StratificationReviewResult()
        
        # 1. 从剖面图结果中提取数据
        if "section" in logic_result_map:
            self._extract_from_section_results(logic_result_map["section"], review_result)
        
        # 2. 从表格结果中提取数据
        if "table" in logic_result_map:
            await self._extract_from_table_results(logic_result_map["table"], review_result)
        
        # 3. 执行数据对比分析
        self._perform_data_comparison(review_result)
        
        # 4. 生成审查报告摘要
        self._generate_review_summary(review_result)
        
        logger.info("分层信息审查完成")
        
        return {
            "drill_holes": {k: [vars(data) for data in v] for k, v in review_result.drill_holes.items()},
            "comparisons": review_result.comparisons,
            "inconsistencies": review_result.inconsistencies,
            "summary": review_result.summary
        }
    
    def _extract_from_section_results(self, section_results: List[DetectedSectionResult], 
                                    review_result: StratificationReviewResult) -> None:
        """
        从剖面图结果中提取钻孔数据
        利用depth_elevation_pairs数组索引作为层级顺序
        
        Args:
            section_results: 剖面图检测结果列表
            review_result: 审查结果对象，用于存储提取的数据
        """
        logger.info(f"处理剖面图数据，共 {len(section_results)} 个结果")
        
        for result in section_results:
            if not hasattr(result, 'sections') or not result.sections:
                continue
                
            for section in result.sections:
                if not section.drill_holes:
                    continue
                    
                for drill_hole in section.drill_holes:
                    # 从深度-高程对中按层级提取数据
                    if drill_hole.depth_elevation_pairs:
                        for layer_index, pair in enumerate(drill_hole.depth_elevation_pairs, 1):
                            # 为每个层级创建单独的钻孔数据对象
                            drill_data = DrillHoleData(
                                drill_hole.drill_name, 
                                "剖面图",
                                layer_level=str(layer_index),  # 使用数字作为层级标识
                                layer_index=layer_index
                            )
                            drill_data.depth = pair.depth
                            drill_data.elevation = pair.elevation
                            drill_data.additional_data = {
                                "pair_data": vars(pair),
                                "surface_elevation": drill_hole.surface_elevation,
                                "samples": [vars(sample) for sample in drill_hole.samples if hasattr(sample, 'depth') and abs(sample.depth - pair.depth) < 0.1]
                            }
                            
                            review_result.drill_holes[drill_hole.drill_name].append(drill_data)
                    else:
                        logger.warning(f"剖面图钻孔 {drill_hole.drill_name} 没有深度-高程对数据")
    
    async def _extract_from_table_results(self, table_results: List[DetectedTableResult], 
                                        review_result: StratificationReviewResult) -> None:
        """
        从表格结果中提取钻孔数据
        支持三种表格类型的数据提取
        
        Args:
            table_results: 表格检测结果列表
            review_result: 审查结果对象
        """
        logger.info(f"处理表格数据，共 {len(table_results)} 个结果")
        
        for table_result in table_results:
            if not hasattr(table_result, 'vision_result') or not table_result.vision_result:
                continue
            
            vision_results = table_result.vision_result
            
            # 识别表格类型并提取数据
            table_type = await self._identify_table_type(vision_results)
            
            if "钻孔柱状图" in table_type:
                await self._extract_drill_bar_chart_data(vision_results, review_result)
            elif "静力触探测试成果图表" in table_type:
                await self._extract_static_penetration_chart_data(vision_results, review_result)
            elif "静力触探分层参数表" in table_type:
                await self._extract_static_penetration_param_data(vision_results, review_result)
            else:
                logger.warning(f"未识别的表格类型: {table_type}")
    
    async def _identify_table_type(self, vision_results: List[DetectionResult]) -> str:
        """
        识别表格类型
        
        Args:
            vision_results: OCR识别结果列表
            
        Returns:
            str: 表格类型描述
        """
        all_text = " ".join([result.original_text for result in vision_results if result.original_text])
        
        if "钻孔柱状图" in all_text:
            return "钻孔柱状图"
        elif "静力触探" in all_text and ("测试成果" in all_text or "土层编号" in all_text):
            return "静力触探测试成果图表"
        elif "静力触探" in all_text and ("分层参数" in all_text or "孔号：" in all_text):
            return "静力触探分层参数表"
        else:
            return "未知表格类型"
    
    def _parse_layer_index(self, layer_level: str, fallback_index: int) -> int:
        """
        解析层级标识，转换为数字索引
        
        Args:
            layer_level: 层级标识（如①、②、③、1、2、3等）
            fallback_index: 备用索引，当无法解析时使用
            
        Returns:
            int: 层级索引（从1开始）
        """
        if not layer_level:
            return fallback_index
        
        # 处理中文数字符号
        chinese_numbers = {
            '①': 1, '②': 2, '③': 3, '④': 4, '⑤': 5, '⑥': 6, '⑦': 7, '⑧': 8, '⑨': 9, '⑩': 10,
            '⑪': 11, '⑫': 12, '⑬': 13, '⑭': 14, '⑮': 15, '⑯': 16, '⑰': 17, '⑱': 18, '⑲': 19, '⑳': 20
        }
        
        if layer_level in chinese_numbers:
            return chinese_numbers[layer_level]
        
        # 处理阿拉伯数字
        try:
            return int(layer_level)
        except ValueError:
            pass
        
        # 处理罗马数字
        roman_numbers = {
            'I': 1, 'II': 2, 'III': 3, 'IV': 4, 'V': 5, 'VI': 6, 'VII': 7, 'VIII': 8, 'IX': 9, 'X': 10
        }
        
        if layer_level.upper() in roman_numbers:
            return roman_numbers[layer_level.upper()]
        
        logger.warning(f"无法解析层级标识: {layer_level}, 使用备用索引: {fallback_index}")
        return fallback_index
    
    def _generate_layer_level(self, layer_index: int) -> str:
        """
        根据层级索引生成层级标识
        
        Args:
            layer_index: 层级索引（从1开始）
            
        Returns:
            str: 层级标识（使用中文数字符号）
        """
        chinese_symbols = ['①', '②', '③', '④', '⑤', '⑥', '⑦', '⑧', '⑨', '⑩',
                          '⑪', '⑫', '⑬', '⑭', '⑮', '⑯', '⑰', '⑱', '⑲', '⑳']
        
        if 1 <= layer_index <= len(chinese_symbols):
            return chinese_symbols[layer_index - 1]
        else:
            return str(layer_index)  # 超出范围时使用数字
