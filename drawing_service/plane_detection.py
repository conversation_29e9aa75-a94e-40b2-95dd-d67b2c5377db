import math
import re

import cv2
import numpy as np

from drawing_service.base import Detector<PERSON><PERSON><PERSON>, VisionComputer
from models.base import VisionDetectedResult, LeaderLine
from models.pdf_page import PdfPageType
from vision.core.data_types import Point
from vision.obj_det import YoloModelType
from vision.obj_det.yolo_holder import PlaneElemType
from vision.ocr_engine.engine import OcrModelType, OcrEngine
from vision.core import DetectionResult
from vision.core.utils import crop_bboxes
from loguru import logger
from typing import List, Union, Tuple, Dict, Any, Optional
from models.plane_models import DetectedPlainResult, DrillInfo, Drill, Coordinate, DrillSpacing


class PlaneVisionComputer(VisionComputer, DetectorHelper):
    """
    平面图检测器
    """

    def __init__(
            self,
            ocr_model: OcrModelType = OcrModelType.RAPID,
            confidence_threshold: float = 0.5,
            iou_threshold: float = 0.45,
    ):
        super().__init__(PdfPageType.FLOOR_PLAN, ocr_model, confidence_threshold, iou_threshold)

    def handle_vision_detection(self, image):
        # 平面钻口检测
        self.add_large_image_predict(YoloModelType.PLANE_DETECTION, image)

    def handle_ocr_detection(self, image: np.ndarray):
        """
        处理 OCR 检测
        :param image: 输入图像
        :return: None
        """
        # 提取钻孔信息
        self.merge_ocr(self.batch_ocr_processor.process(
            self._filter_in_class(
                self.detections,
                PlaneElemType.DRILL_INFO_TYPE1.value,
                PlaneElemType.DRILL_INFO_TYPE2.value,
                PlaneElemType.DRILL_INFO.value
            ), image, self.ocr_engine.recognize
        ))
        self.merge_ocr(self.batch_ocr_processor.process(
            self._filter_in_class(
                self.detections,
                PlaneElemType.LITTLE_DRILL.value
            ), image, self.ocr_engine.recognize
        ))

    def handle_logic_compute(self, image, detected_result: VisionDetectedResult) -> DetectedPlainResult:
        if len(self.detections) == 0:
            logger.warning("未检测到任何平面图元素")
            return DetectedPlainResult(
                file_name=detected_result.file_name,
                detect_type=self.page_type.desc,
                image_width=detected_result.image_width,
                image_height=detected_result.image_height,
                drills=[],
                coordinates=[],
                drill_infos=[]
            )

        # 坐标
        coordinates = self._find_coordinates(image)
        # 向坐标中插入引线信息
        coordinates = self.bind_coordinates_with_leader_line(coordinates, detected_result.leader_line)
        # 钻口
        drills, detection_drills = self._find_drills(image)
        # 计算距离比值
        _, _, distance_ratio = self.distance_ratio(coordinates)
        logger.debug(f"距离比值: {distance_ratio}")
        # 计算孔距
        drills_distances = self._calculate_drills_distance(drills, distance_ratio)
        logger.debug(f"孔距: {drills_distances}")
        # 钻口信息
        drill_infos, detection_infos = self._find_drill_infos(image)
        # 绑定钻孔和钻孔信息
        self._binding_hole_and_info(drills, detection_drills, drill_infos, detection_infos)
        # 计算真实坐标比例尺
        x_value_per_pixel, y_value_per_pixel = self.calculate_average_scale_per_pixel(coordinates)
        # 计算钻孔的真实坐标
        self._calculate_real_coordinate(drills, coordinates, x_value_per_pixel, y_value_per_pixel)

        logger.info(f"检测完成 - 钻孔: {len(drills)}, 坐标: {len(coordinates)}, 钻孔信息: {len(drill_infos)}")

        return DetectedPlainResult(
            file_name=detected_result.file_name,
            detect_type=self.page_type.desc,
            image_width=detected_result.image_width,
            image_height=detected_result.image_height,
            x_value_per_pixel=x_value_per_pixel,
            y_value_per_pixel=y_value_per_pixel,
            drills=drills,
            coordinates=coordinates,
            drill_infos=drill_infos,
            drill_spacings=drills_distances
        )

    def _find_drills(self, image: np.ndarray) -> Tuple[List[Drill], List[DetectionResult]]:
        """查找所有钻孔并计算圆心坐标"""
        all_drills: List[Drill] = []
        detections: List[DetectionResult] = []

        for detection in self.detections:
            text = None
            if detection.class_name not in [PlaneElemType.DRILL.value, PlaneElemType.LITTLE_DRILL.value]:
                continue

            # 计算圆心坐标
            center_point = None
            if detection.class_name == PlaneElemType.LITTLE_DRILL.value:
                # 对于小钻孔，使用extract_drill函数进行精确定位
                try:
                    # 从原图中根据detection bbox裁剪出图片区域
                    rois, _ = crop_bboxes(image, [detection.xyxy])
                    if rois:
                        # 调用extract_drill函数获取圆心坐标
                        points = self.extract_drill(rois[0])
                        if points:
                            # 取第一个检测到的圆心，并转换为原图坐标系
                            local_point = points[0]
                            x1, y1, _, _ = detection.xyxy
                            center_point = Point(
                                x=local_point.x + x1,
                                y=local_point.y + y1
                            )
                    # 提取范围内的文本（钻孔编号）
                    text = self.ocr_engine.find_best_text(detection, self.roi_ocr_result)

                    logger.debug(f"小钻孔OCR识别结果: {text}")
                except Exception as e:
                    logger.warning(f"小钻孔圆心坐标计算失败: {e}")
                    # 如果extract_drill失败，使用bbox中心点作为备选
                    x1, y1, x2, y2 = detection.xyxy
                    center_point = Point(x=(x1 + x2) / 2, y=(y1 + y2) / 2)
            elif detection.class_name == PlaneElemType.DRILL.value:
                # 对于普通钻孔，使用bbox中心点计算圆心坐标
                x1, y1, x2, y2 = detection.xyxy
                center_point = Point(x=(x1 + x2) / 2, y=(y1 + y2) / 2)

            # 创建钻孔对象
            drill = Drill(
                **detection.model_dump(),
                type=detection.class_name,
                center_point=center_point
            ).model_copy(update=dict(
                original_text=text))

            all_drills.append(drill)
            detections.append(detection)

        small_hole_cnt = sum(1 for d in all_drills if d.class_name == PlaneElemType.LITTLE_DRILL.value)
        big_hole_cnt = sum(1 for d in all_drills if d.class_name == PlaneElemType.DRILL.value)
        logger.info(f"找到钻孔共 {len(all_drills)} 个, 其中小钻孔 {small_hole_cnt} 个，大钻孔 {big_hole_cnt} 个")
        return all_drills, detections

    def _find_coordinates(self, image: np.ndarray) -> List[Coordinate]:
        """查找所有坐标（普通坐标 + 带线坐标）"""
        all_coordinates = []

        # 处理每一类坐标
        for detection in self.detections:
            if detection.class_name not in [PlaneElemType.COORDINATE]:
                continue

            # 提取并解析坐标文本
            coord_text = self._extract_text_from_detection(image, detection)
            x_coord, y_coord = self._parse_coordinate_text(coord_text)

            all_coordinates.append(Coordinate(
                **detection.model_dump(),
                point=None,  # 后续处理
                x_coordinate=x_coord,
                y_coordinate=y_coord,
                is_with_lined=False
            ).model_copy(update=dict(
                id=detection.id,
                original_text=coord_text
            )))

        # 记录日志
        logger.info(f"找到坐标 {len(all_coordinates)} 个")

        return all_coordinates

    def _find_drill_infos(self, image) -> Tuple[List[DrillInfo], List[DetectionResult]]:
        """查找所有钻孔信息"""
        all_drill_infos: List[DrillInfo] = []
        detection_infos: List[DetectionResult] = []

        # 查找各种类型的钻孔信息
        drill_info_types = [
            PlaneElemType.DRILL_INFO.value,
            PlaneElemType.DRILL_INFO_TYPE1.value,
            PlaneElemType.DRILL_INFO_TYPE2.value
        ]

        for detection in self.detections:
            if detection.class_name not in drill_info_types:
                continue

            info_text = OcrEngine.find_best_text(detection, self.roi_ocr_result)

            parsed_info = self._parse_drill_info_text(info_text)

            all_drill_infos.append(DrillInfo(
                **detection.model_dump(),
                type=detection.class_name,
                drill_number=parsed_info.get("drill_number"),
                drill_elevation=parsed_info.get("drill_elevation"),
                drill_depth=parsed_info.get("drill_depth"),
                water_level_elevation=parsed_info.get("water_level_elevation")
            ).model_copy(update=dict(
                original_text=info_text
            )))
            detection_infos.append(detection)

        # 输出日志：总共找到了多少个钻孔信息
        logger.info(f"找到钻孔信息: {len(all_drill_infos)} 个")

        # 返回所有提取到的钻孔信息
        return all_drill_infos, detection_infos

    def _binding_hole_and_info(
            self,
            drills: List[Drill], detection_drills: List[DetectionResult],
            infos: List[DrillInfo], detection_infos: List[DetectionResult]) -> None:
        """将钻孔和钻孔信息进行绑定"""
        if not drills or not detection_drills or not infos or not detection_infos:
            return

        distance_map = {}
        # 计算每一个钻孔与每一个钻孔信息的最短距离
        for idx, (drill, detection_drill) in enumerate(zip(drills, detection_drills)):
            if detection_drill.class_name != PlaneElemType.DRILL.value:
                continue

            distance_map[idx] = []
            for (info, detection_info) in zip(infos, detection_infos):
                distance_map[idx].append((detection_drill.min_distance(detection_info), info))
            # 按照距离升序
            distance_map[idx].sort(key=lambda x: x[0])

        # 记录已经分配的 info
        assigned_keys = set()

        # 全局排序：按照每个钻孔最近 info 的距离升序
        sorted_drills = sorted(distance_map.items(), key=lambda item: item[1][0][0])

        # 依次绑定
        for drill_idx, candidates in sorted_drills:
            for dist, info in candidates:
                key = (info.type, info.drill_number, info.drill_elevation,
                       info.drill_depth, info.water_level_elevation)
                if key not in assigned_keys:
                    drills[drill_idx].binding_info = info
                    assigned_keys.add(key)
                    break

        big_hole_cnt = sum(1 for d in drills if d.class_name == PlaneElemType.DRILL.value)
        message = f"存在 {big_hole_cnt} 个大钻孔，{len(infos)} 个钻口信息，绑定了 {len(assigned_keys)} 个钻孔信息"
        if big_hole_cnt != len(assigned_keys):
            message += "，可能存在异常绑定"
            logger.warning(message)
        else:
            logger.info(message)

    def _parse_coordinate_text(self, text: str) -> Tuple[float, float]:
        """解析坐标文本"""
        try:
            coord_patterns = [
                r'X=\s*[-+]?\d+(?:\.\d{1,3})?',  # 匹配 X= 后跟可选符号、整数部分和最多三位小数
                r'Y=\s*[-+]?\d+(?:\.\d{1,3})?'  # 匹配 Y= 后跟可选符号、整数部分和最多三位小数
            ]

            x_coord, y_coord = 0.0, 0.0

            if not isinstance(text, str):
                logger.warning(f"输入不是字符串: {type(text)}, 值为: {repr(text)}")
                return x_coord, y_coord

            if not text.strip():
                logger.warning("输入文本为空")
                return x_coord, y_coord

            match_x = re.search(r'X\s*[:=]?\s*([-+]?\d+(?:\.\d+)?)', text, re.IGNORECASE)
            if match_x:
                try:
                    x_coord = float(match_x.group(1))
                except ValueError:
                    logger.warning(f"文本：{text}, 其中 X 值无法转为浮点数: {match_x.group(1)}")
            else:
                logger.debug(f"未在文本 \"{text}\" 中找到 X 坐标")

            match_y = re.search(r'Y\s*[:=]?\s*([-+]?\d+(?:\.\d+)?)', text, re.IGNORECASE)
            if match_y:
                try:
                    y_coord = float(match_y.group(1))
                except ValueError:
                    logger.warning(f"Y 值无法转为浮点数: {match_y.group(1)}")
            else:
                logger.debug(f"未在文本 \"{text}\" 中找到 Y 坐标")

            return x_coord, y_coord
        except Exception as e:
            logger.warning(f"解析坐标文本失败: {e}")
            return 0.0, 0.0

    def _parse_drill_info_text(self, text: str) -> Dict[str, Any]:
        """解析钻孔信息文本"""
        drill_number = ""
        drill_elevation = None
        drill_depth = None
        water_level_elevation = None

        try:
            if not isinstance(text, str) or not text.strip():
                logger.warning(f"无效输入文本: {repr(text)}")
                return {
                    "drill_number": "",
                    "drill_elevation": None,
                    "drill_depth": None,
                    "water_level_elevation": None
                }

            text = text.replace("\n", " ").strip()

            # 1. 提取钻孔编号：支持 字母+数字、字母+特殊符号-+数字、纯数字
            match_id = re.search(r'[A-Z]+\d*[A-Z]*[-]?\d+|[A-Z]+[-]?\d+', text, re.IGNORECASE)
            if match_id:
                drill_number = match_id.group(0)
            else:
                # 如果没有包含字母的编号，则匹配纯数字编号
                match_id = re.search(r'\b\d+\b(?![.\d])', text)
                if match_id:
                    drill_number = match_id.group(0)

            # 2. 提取所有 xx.xx 形式的浮点数（允许1~3位小数），包括带单位的浮点数
            float_texts = re.findall(r'\b\d+\.\d{1,3}(?:[a-zA-Z]+)?\b', text)
            numbers = [float(re.search(r'\d+\.\d{1,3}', x).group()) for x in float_texts if re.search(r'\d+\.\d{1,3}', x)]

            # 3. 特别处理：检查是否有类似 "G34.37" 的结构，提取其中的 34.37
            # 如果第一个 token 是字母+数字+小数，比如 G34.37
            first_word = text.split()[0] if text.split() else ""
            match_mixed = re.match(r'^[A-Z]+(\d+\.\d{1,3})$', first_word)
            if match_mixed and len(numbers) >= 2:
                # 第一个数字是 G34.37 中的 34.37
                drill_elevation = float(match_mixed.group(1))
                # 剩下的两个数字是 深度 和 水位
                if len(numbers) >= 2:
                    drill_depth = numbers[0]  # 第一个纯数字是深度
                    water_level_elevation = numbers[1] if len(numbers) >= 2 else None
            elif len(numbers) >= 3:
                # 如果没有混合格式，直接取前三个
                drill_elevation = numbers[0]
                drill_depth = numbers[1]
                water_level_elevation = numbers[2]
            elif len(numbers) == 2:
                drill_elevation = numbers[0]
                drill_depth = numbers[1]
                water_level_elevation = None
            elif len(numbers) == 1:
                drill_elevation = numbers[0]
                drill_depth = None
                water_level_elevation = None

            logger.debug(
                f"解析 \"{text}\" 结果: 编号={drill_number}, 高程={drill_elevation}, 深度={drill_depth}, 水位={water_level_elevation}")
            return {
                "drill_number": drill_number,
                "drill_elevation": drill_elevation,
                "drill_depth": drill_depth,
                "water_level_elevation": water_level_elevation
            }

        except Exception as e:
            logger.error(f"解析钻孔信息文本失败: {e}", exc_info=True)
            return {
                "drill_number": "",
                "drill_elevation": None,
                "drill_depth": None,
                "water_level_elevation": None
            }

    @staticmethod
    def extract_drill(image_input: Union[str, np.ndarray]) -> List[Point]:
        """
        检测单张图像中的圆，并返回圆心坐标（Point 类列表）

        Args:
            image_input: 图像文件路径或numpy数组

        Returns:
            List[Point]: 圆心坐标列表
        """
        if isinstance(image_input, str):
            image = cv2.imread(image_input)
            if image is None:
                logger.error(f"图像加载失败：{image_input}")
                return []
        elif isinstance(image_input, np.ndarray):
            image = image_input.copy()
        else:
            logger.error(f"不支持的图像输入类型: {type(image_input)}")
            return []

        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        blurred = cv2.medianBlur(gray, 5)

        # 圆检测
        circles = cv2.HoughCircles(
            blurred,
            cv2.HOUGH_GRADIENT,
            dp=1.2,
            minDist=40,
            param1=100,
            param2=50,
            minRadius=5,
            maxRadius=50
        )

        points: List[Point] = []

        if circles is not None:
            circles = np.uint16(np.around(circles))
            for x, y, r in circles[0, :]:
                points.append(Point(x=float(x), y=float(y)))  # 转为 float 以符合 BaseModel
        if len(points) >= 2:
            logger.warning(f"解析到 {len(points)} 个圆心坐标，可能存在误检")
        return points

    # 计算钻孔真实坐标
    def _calculate_real_coordinate(self, drills, coordinates, x_value_per_pixel, y_value_per_pixel):
        if len(coordinates) < 1:
            return
        cor = coordinates[0]
        for drill in drills:
            if drill.center_point and x_value_per_pixel and y_value_per_pixel:
                dx = drill.center_point.x - cor.point.x
                dy = drill.center_point.y - cor.point.y
                real_x = cor.x_coordinate + dx * x_value_per_pixel
                real_y = cor.y_coordinate + dy * y_value_per_pixel
                drill.real_coordinate = Point(x=real_x, y=real_y)

    @staticmethod
    def _is_valid_coordinate(coordinate: Coordinate) -> bool:
        """
        检查坐标是否有效

        :param coordinate: 要检查的坐标
        :return: 如果坐标有效返回True，否则返回False
        """
        return (coordinate is not None and
                coordinate.point is not None and
                coordinate.point.x is not None and
                coordinate.point.y is not None and
                coordinate.x_coordinate is not None and
                coordinate.y_coordinate is not None)

    def calculate_average_scale_per_pixel(self, coordinates: List[Coordinate]) -> Tuple[Optional[float], Optional[float]]:
        """
        计算 X、Y 轴每像素对应的真实值（支持多对点，取平均值）

        :param coordinates: 坐标列表，至少需要 2 个
        :return: tuple[0] = x轴每像素真实值, tuple[1] = y轴每像素真实值
        """
        if not coordinates or len(coordinates) < 2:
            logger.warning("至少需要两个坐标点来计算比例")
            return None, None

        total_x_scale = 0.0
        total_y_scale = 0.0
        count = 0

        # 两两比较计算比例
        for i in range(len(coordinates)):
            for j in range(i + 1, len(coordinates)):
                c1 = coordinates[i]
                c2 = coordinates[j]

                if not self._is_valid_coordinate(c1) or not self._is_valid_coordinate(c2):
                    continue

                dx_real = c2.x_coordinate - c1.x_coordinate
                dx_pixel = c2.point.x - c1.point.x

                dy_real = c2.y_coordinate - c1.y_coordinate
                dy_pixel = c2.point.y - c1.point.y

                if dx_pixel != 0 and dy_pixel != 0:
                    total_x_scale += dx_real / dx_pixel
                    total_y_scale += dy_real / dy_pixel
                    count += 1

        if count == 0:
            logger.warning("无法计算比例，因为没有有效的坐标对")
            return None, None

        logger.debug(f"计算比例: X={total_x_scale / count}, Y={total_y_scale / count}, 使用了 {count} 对坐标"
                     f", 总坐标数 {len(coordinates)}, total_x_scale={total_x_scale}, total_y_scale={total_y_scale}")
        return total_x_scale / count, total_y_scale / count

    # 绑定坐标和引线
    def bind_coordinates_with_leader_line(self, coordinates: List[Coordinate], leader_lines: List[LeaderLine]):
        for line in leader_lines:
            for coord in coordinates:
                if line.bbox_id == coord.id:
                    coord.point = line.point
        return coordinates

    def _calculate_drills_distance(self, drills: List[Drill], ratio: float = 1.0) -> List[DrillSpacing]:
        """
        计算所有钻孔之间的距离
        :param drills: 钻孔列表
        :param ratio: 比例尺
        :return: 包含钻孔对和距离的字典列表
        """

        ans = []
        for i in range(len(drills) - 1):
            for j in range(i + 1, len(drills)):
                # 计算每个钻孔的坐标中心点
                center_point1 = drills[i].center_point
                center_point2 = drills[j].center_point
                if center_point1 and center_point2:
                    distance = self.distance_calculator(center_point1.x, center_point1.y, center_point2.x,
                                                        center_point2.y, ratio)
                    ans.append(DrillSpacing(
                        drill1=drills[i],
                        drill2=drills[j],
                        plane_spacing=distance
                    ))
        return ans


if __name__ == '__main__':

    image_url = r"C:\CodeMgr\07-SHGCWithAI\Image\202单体扩建项目地勘\202单体扩建项目地勘_cd086b98-3b0d-4c11-9920-2cc89de69ce1_21.png"

    try:
        detector = PlaneVisionComputer(OcrModelType.RAPID, 0.5, 0.45)
        result = detector.detect("test.jpg", image_url)
        print(result.model_dump_json(indent=2))
    except Exception as e:
        logger.error(f"检测失败: {e}")
        raise e


