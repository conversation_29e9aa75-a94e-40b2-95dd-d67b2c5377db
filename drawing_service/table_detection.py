from typing import Any, List

import cv2
import numpy as np
from paddleocr import LayoutDetection

from drawing_service.base import VisionComputer, DetectorHelper
from models.base import VisionDetectedResult
from models.pdf_page import PdfPageType
from models.sheet_models import SheetConfig, DetectedTableResult
from settings import OUTPUT
from utils.grouper import Grouper
from vision.core import <PERSON>Handler
from vision.core.data_types import Bo<PERSON>ingBox, DetectionResult
from vision.core.utils import Visualizer
from vision.cv_det.opencv_detector import OpenCVDetector
from vision.ocr_engine.engine import OcrModelType
from vision.table_det import PPStructureModelManager, PPStructureModelType
from vision.table_det.cell_coordinate_mapper import CoordinateMapper
from vision.table_det.cell_to_table_convertor import convert_bboxes_to_sheet_config
from vision.table_det.table_image_cutter import TableImageCutter

TABLE_ELEM_LABEL = "table"
TABLE_CELL = "my_table_cell"


class ComplexTableVisionComputer(VisionComputer, DetectorHelper):
    """错位表格检测器"""

    def __init__(
            self,
            ocr_model: OcrModelType = OcrModelType.RAPID,
            confidence_threshold: float = 0.5,
            iou_threshold: float = 0.45
    ):
        super().__init__(PdfPageType.TABLE, ocr_model, confidence_threshold, iou_threshold)
        self.batch_size = 8
        self.target_w = 512
        self.target_h = 512

    def handle_vision_detection(self, image: np.ndarray):
        model = PPStructureModelManager().load_model(PPStructureModelType.LAYOUT_DETECTION)
        outputs = model.predict(image, batch_size=1, layout_nms=True)

        # 文档布局检测
        for res in outputs:
            for idx, box in enumerate(res['boxes']):
                self.detections.append(DetectionResult(
                    class_name=box['label'],
                    **BoundingBox.from_array(box['coordinate']).model_dump()
                ))

        image_width, image_height = image.shape[:2]

        # 裁剪表格区域
        table_elem = self._filter_by_class(self.detections, TABLE_ELEM_LABEL)
        crop_images = [ImageHandler.crop_bbox(image, detection) for detection in table_elem]

        # 坐标映射器
        coordinate_mapper = CoordinateMapper(original_size=(image_width, image_height))

        # 分割表格检测
        for idx, (crop_img, detection) in enumerate(zip(crop_images, table_elem)):
            # 检测水平线以及垂直线
            opencv_detector = OpenCVDetector(image=crop_img)
            horizontal_lines = opencv_detector.detect_horizontal_lines()
            vertical_lines = opencv_detector.detect_vertical_lines()

            # 分割表格
            cutter = TableImageCutter(crop_img, threshold_ratio=0.9, min_segment_ratio=0.02)
            cutter.set_lines(horizontal_lines, vertical_lines)
            cutter.visualize_cut_lines(target_width=self.target_w, target_height=self.target_h, output_path=OUTPUT / f"cut_preview_{idx}.png")
            crop_tables, crop_areas = cutter.cut_image(target_width=self.target_w, target_height=self.target_h)

            for id_, crop_table in enumerate(crop_tables):
                cv2.imwrite(f"table_crop_{idx}_{id_}.png", crop_table)

            # 识别表格单元格
            batch_size = min(self.batch_size, len(crop_tables))
            model = PPStructureModelManager().load_model(PPStructureModelType.TABLE_CELL_DET)
            detected_cells = model.predict(crop_tables, batch_size=batch_size)

            # 映射回原表格坐标
            original_coordinates = []
            for part_cells, area in zip(detected_cells, crop_areas):
                cells = [BoundingBox.xyxy_to_corner4(box['coordinate']) for box in part_cells['boxes']]
                original_coordinate = coordinate_mapper.map_from_crop_region(cells, area)
                original_coordinates.extend(original_coordinate)

            # 映射到原图
            original_coordinates = [
                DetectionResult(
                    class_name=TABLE_CELL,
                    image_index=idx,
                    **BoundingBox.from_corner4(coord).map_to_original(detection).model_dump()
                ) for coord in original_coordinates
            ]
            self.detections.extend(original_coordinates)

    def handle_ocr_detection(self, image: np.ndarray):
        self.merge_ocr(self.batch_ocr_processor.process(
            self._filter_in_class(self.detections, TABLE_CELL),
            image, self.ocr_engine.recognize
        ))

    def handle_logic_compute(self, image, vision_result: VisionDetectedResult) -> DetectedTableResult:
        image_width, image_height = vision_result.image_width, vision_result.image_height

        table_cells = self._filter_in_class(self.detections, TABLE_CELL)
        table_num = len(set([cell.image_index for cell in table_cells]))

        table_infos: List[SheetConfig] = []
        for idx in range(table_num):
            table_idx_cells = [cell for cell in table_cells if cell.image_index == idx]
            texts = [self.ocr_engine.find_best_text(cell, self.roi_ocr_result) for cell in table_idx_cells]

            x_max, y_max = max(b.x_max for b in table_idx_cells), max(b.y_max for b in table_idx_cells)
            img = np.full((y_max, x_max, 3), 255, dtype=np.uint8)
            img = Visualizer().draw_bboxes_with_texts(img, table_idx_cells, texts)
            cv2.imwrite(f"table_{idx}.png", img)

            table_infos.append(convert_bboxes_to_sheet_config(table_idx_cells, texts=texts))

        return DetectedTableResult(
            file_name=vision_result.file_name,
            detect_type=self.page_type.desc,
            image_width=image_width,
            image_height=image_height,
            sheets=table_infos,
            vision_result=self.detections,
            roi_ocr_result=self.roi_ocr_result
        )


if __name__ == '__main__':
    image_path = "/Users/<USER>/CodeRepo/08-CADWithAi/data/images/drawing_classify/train/表格为主/0416-shang_page_154.png"

    computer = ComplexTableVisionComputer()
    # result = computer.detect("", image_path)
    result = computer.vision_detect("", image_path)
    for sheet in result.sheets:
        print(sheet.model_dump_json())
    # import pickle
    # with open('texts.pkl', 'rb') as f:  # 'rb' 是以二进制读取模式打开文件
    #     texts = pickle.load(f)
    #
    #
    # with open('original_coordinates.pkl', 'rb') as f:  # 'rb' 是以二进制读取模式打开文件
    #     original_coordinates = pickle.load(f)
    #
    # sheet_config = convert_bboxes_to_sheet_config(original_coordinates, texts=texts)
    #
    # result = DetectedTableResult(
    #     file_name="",
    #     detect_type="复杂表格",
    #     image_width=1024,
    #     image_height=1024,
    #     sheets=[sheet_config],
    # )
    #
    # height_list = [box.height for box in original_coordinates]
    # group = Grouper(tolerance=0.1).simple_group(height_list, lambda x: x)
    # grouped_height = [[height_list[i] for i in g] for g in group]
    #
    # print(result.model_dump_json())
