from typing import List, Dict, Tuple, Any
from collections import defaultdict

from models.sheet_models import Sheet<PERSON>onfig, MergeCell
from vision.core.data_types import BoundingBox


def convert_bboxes_to_sheet_config(
        bboxes: List[BoundingBox],
        texts: List[str],
        sheet_name: str = "Sheet1",
        height_overlap_threshold: float = 0.95,
        width_overlap_threshold: float = 0.95
) -> SheetConfig:
    """
    将边界框列表转换为 SheetConfig

    Args:
        bboxes: 单元格边界框列表
        texts: 对应的文本内容列表
        sheet_name: 工作表名称
        height_overlap_threshold: 高度重合阈值，用于判断同行
        width_overlap_threshold: 宽度重合阈值，用于判断同列
    """
    if not bboxes or len(bboxes) != len(texts):
        raise ValueError("边界框列表和文本列表长度不匹配或为空")

    # 1. 预处理：计算每个bbox的尺寸并进行分组统计
    processed_cells = []
    heights = []
    widths = []

    for i, (bbox, text) in enumerate(zip(bboxes, texts)):
        width = bbox.x2 - bbox.x1
        height = bbox.y2 - bbox.y1
        processed_cells.append({
            'index': i,
            'bbox': bbox,
            'text': text,
            'width': width,
            'height': height,
            'center_x': (bbox.x1 + bbox.x2) / 2,
            'center_y': (bbox.y1 + bbox.y2) / 2
        })
        heights.append(height)
        widths.append(width)

    # 2. 高度和宽度分组（用于后续补全缺失单元格）
    height_groups = _group_similar_values(heights)
    width_groups = _group_similar_values(widths)

    # 3. 按位置排序（从上到下，从左到右）
    processed_cells.sort(key=lambda x: (x['bbox'].y1, x['bbox'].x1))

    # 4. 构建网格布局
    grid_layout = _build_grid_layout(processed_cells, height_overlap_threshold)

    # 5. 检测并处理合并单元格
    merge_cells, grid_data = _detect_merge_cells_and_build_data(
        grid_layout, processed_cells, height_groups, width_groups
    )

    # 6. 构建 SheetConfig
    return SheetConfig(
        name=sheet_name,
        data=grid_data,
        data_format="array",
        has_headers=True if grid_data else False,
        merge_cells=merge_cells,
        start_row=1,
        start_col=1
    )


def _group_similar_values(values: List[float], tolerance: float = 0.1) -> Dict[int, List[int]]:
    """将相似的数值进行分组"""
    groups = defaultdict(list)
    sorted_indices = sorted(range(len(values)), key=lambda i: values[i])

    group_id = 0
    current_group_value = values[sorted_indices[0]]

    for idx in sorted_indices:
        value = values[idx]
        if abs(value - current_group_value) <= current_group_value * tolerance:
            groups[group_id].append(idx)
        else:
            group_id += 1
            current_group_value = value
            groups[group_id].append(idx)

    return dict(groups)


def _calculate_overlap_ratio(bbox1: BoundingBox, bbox2: BoundingBox, direction: str = 'height') -> float:
    """计算两个边界框在指定方向上的重合比例"""
    if direction == 'height':
        # 计算垂直方向重合
        overlap = max(0, min(bbox1.y2, bbox2.y2) - max(bbox1.y1, bbox2.y1))
        min_height = min(bbox1.y2 - bbox1.y1, bbox2.y2 - bbox2.y1)
        return overlap / min_height if min_height > 0 else 0
    else:  # width
        # 计算水平方向重合
        overlap = max(0, min(bbox1.x2, bbox2.x2) - max(bbox1.x1, bbox2.x1))
        min_width = min(bbox1.x2 - bbox1.x1, bbox2.x2 - bbox2.x1)
        return overlap / min_width if min_width > 0 else 0


def _build_grid_layout(processed_cells: List[Dict], height_overlap_threshold: float) -> List[List[Dict]]:
    """构建网格布局，识别行和列"""
    rows = []
    used_cells = set()

    for cell in processed_cells:
        if cell['index'] in used_cells:
            continue

        # 找到与当前单元格同行的所有单元格
        current_row = [cell]
        used_cells.add(cell['index'])

        for other_cell in processed_cells:
            if (other_cell['index'] not in used_cells and
                    _calculate_overlap_ratio(cell['bbox'], other_cell['bbox'], 'height') >= height_overlap_threshold):
                current_row.append(other_cell)
                used_cells.add(other_cell['index'])

        # 按 x 坐标排序
        current_row.sort(key=lambda x: x['bbox'].x1)
        rows.append(current_row)

    return rows


def _detect_merge_cells_and_build_data(
        grid_layout: List[List[Dict]],
        processed_cells: List[Dict],
        height_groups: Dict[int, List[int]],
        width_groups: Dict[int, List[int]]
) -> Tuple[List[MergeCell], List[List[Any]]]:
    """检测合并单元格并构建数据矩阵"""

    # 计算网格的最大列数
    max_cols = max(len(row) for row in grid_layout) if grid_layout else 0

    # 估算标准单元格尺寸（用于补全缺失单元格）
    standard_height = _get_most_common_value(height_groups)
    standard_width = _get_most_common_value(width_groups)

    merge_cells = []
    grid_data = []

    for row_idx, row_cells in enumerate(grid_layout):
        data_row = [''] * max_cols
        col_idx = 0

        for cell in row_cells:
            # 检查是否为合并单元格
            cell_height = cell['height']
            cell_width = cell['width']

            # 估算跨行数和跨列数
            span_rows = max(1, round(cell_height / standard_height))
            span_cols = max(1, round(cell_width / standard_width))

            # 确保不超出网格范围
            end_row = min(row_idx + span_rows - 1, len(grid_layout) - 1)
            end_col = min(col_idx + span_cols - 1, max_cols - 1)

            # 如果是合并单元格，添加到合并列表
            if span_rows > 1 or span_cols > 1:
                start_cell = _number_to_excel_column(col_idx + 1) + str(row_idx + 1)
                end_cell = _number_to_excel_column(end_col + 1) + str(end_row + 1)
                merge_range = f"{start_cell}:{end_cell}"

                merge_cells.append(MergeCell(
                    range=merge_range,
                    value=cell['text']
                ))

            # 填充数据
            if col_idx < len(data_row):
                data_row[col_idx] = cell['text']

            col_idx = end_col + 1

        grid_data.append(data_row)

    # 补全缺失的单元格（如果需要的话）
    grid_data = _fill_missing_cells(grid_data, max_cols)

    return merge_cells, grid_data


def _get_most_common_value(groups: Dict[int, List[int]]) -> float:
    """获取最常见的数值（用于估算标准单元格尺寸）"""
    if not groups:
        return 20  # 默认值

    # 找到最大的组（包含最多元素的组）
    largest_group = max(groups.values(), key=len)
    if largest_group:
        # 这里需要从原始数据中获取实际值，简化处理返回一个估算值
        return 20  # 实际实现中应该返回该组的平均值
    return 20


def _number_to_excel_column(n: int) -> str:
    """将数字转换为 Excel 列名（A, B, C, ..., Z, AA, AB, ...）"""
    result = ""
    while n > 0:
        n -= 1  # 调整为 0 基数
        result = chr(ord('A') + n % 26) + result
        n //= 26
    return result


def _fill_missing_cells(grid_data: List[List[Any]], target_cols: int) -> List[List[Any]]:
    """补全缺失的单元格，确保每行都有相同的列数"""
    for row in grid_data:
        while len(row) < target_cols:
            row.append('')
    return grid_data
