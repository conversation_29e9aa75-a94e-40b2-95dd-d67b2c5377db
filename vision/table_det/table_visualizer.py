import cv2
import numpy as np
from typing import List, Tuple

from vision.core.data_types import BoundingBox


class BBoxVisualizer:
    """Excel 边界框可视化器 - 专为Excel视觉采集数据设计"""

    def __init__(self):
        # 预定义颜色 (BGR格式)
        self.colors = [
            (0, 0, 255),  # 红色
            (255, 0, 0),  # 蓝色
            (0, 255, 0),  # 绿色
            (0, 165, 255),  # 橙色
            (255, 0, 255),  # 紫色
            (0, 255, 255),  # 黄色
            (255, 255, 0),  # 青色
            (128, 0, 128),  # 紫色
            (0, 128, 255),  # 橙红色
            (255, 192, 203),  # 粉色
        ]

    def calculate_canvas_size(self, bboxes: List[BoundingBox],
                              min_width: int = 800, min_height: int = 600) -> Tuple[int, int]:
        """计算画布尺寸"""
        max_x = max_y = 0

        for bbox in bboxes:
            if bbox.x1 is not None and bbox.y1 is not None and bbox.x2 is not None and bbox.y2 is not None:
                max_x = max(max_x, bbox.x2)
                max_y = max(max_y, bbox.y2)

        # 添加边距
        margin = 50
        width = max(max_x + margin * 2, min_width)
        height = max(max_y + margin * 2, min_height)

        return int(width), int(height)

    def draw_text_with_background(self, img: np.ndarray, text: str,
                                  position: Tuple[int, int], color: Tuple[int, int, int],
                                  font_scale: float = 0.6, thickness: int = 1):
        """绘制带背景的文本"""
        font = cv2.FONT_HERSHEY_SIMPLEX

        # 获取文本尺寸
        (text_width, text_height), baseline = cv2.getTextSize(text, font, font_scale, thickness)

        x, y = position

        # 绘制背景矩形
        background_color = (255, 255, 255)  # 白色背景
        cv2.rectangle(img,
                      (x - 2, y - text_height - 5),
                      (x + text_width + 2, y + baseline),
                      background_color, -1)

        # 绘制边框
        cv2.rectangle(img,
                      (x - 2, y - text_height - 5),
                      (x + text_width + 2, y + baseline),
                      color, 1)

        # 绘制文本
        cv2.putText(img, text, (x, y - 2), font, font_scale, color, thickness)

    def visualize_excel_bboxes(self, bboxes: List[BoundingBox], texts: List[str],
                               output_path: str = "excel_bbox_visualization.png",
                               background_color: Tuple[int, int, int] = (240, 240, 240),
                               show_indices: bool = True,
                               bbox_thickness: int = 2) -> np.ndarray:
        """
        可视化Excel边界框数据

        Args:
            bboxes: 边界框列表
            texts: 对应的文本列表
            output_path: 输出图片路径
            background_color: 背景颜色 (BGR)
            show_indices: 是否显示索引号
            bbox_thickness: 边界框线条粗细

        Returns:
            生成的图像数组
        """
        if len(bboxes) != len(texts):
            raise ValueError(f"边界框数量({len(bboxes)})和文本数量({len(texts)})必须相等")

        # 过滤有效的边界框
        valid_data = []
        for i, (bbox, text) in enumerate(zip(bboxes, texts)):
            if (bbox.x1 is not None and bbox.y1 is not None and
                    bbox.x2 is not None and bbox.y2 is not None):
                valid_data.append((i, bbox, text))

        if not valid_data:
            raise ValueError("没有有效的边界框数据")

        print(f"找到 {len(valid_data)} 个有效边界框")

        # 计算画布尺寸
        width, height = self.calculate_canvas_size([bbox for _, bbox, _ in valid_data])

        # 创建白色画布
        img = np.full((height, width, 3), background_color, dtype=np.uint8)

        # 绘制标题
        title = f"Excel 边界框可视化 - 共 {len(valid_data)} 个区域"
        cv2.putText(img, title, (20, 30), cv2.FONT_HERSHEY_SIMPLEX,
                    0.8, (50, 50, 50), 2)

        # 绘制每个边界框
        for idx, (original_idx, bbox, text) in enumerate(valid_data):
            color = self.colors[idx % len(self.colors)]

            # 绘制边界框矩形
            cv2.rectangle(img, (bbox.x1, bbox.y1), (bbox.x2, bbox.y2),
                          color, bbox_thickness)

            # 准备文本标签
            if show_indices:
                label = f"[{original_idx}] {text}"
            else:
                label = text

            # 限制文本长度以避免显示过长
            if len(label) > 30:
                label = label[:27] + "..."

            # 计算文本位置
            text_x = bbox.x1
            text_y = bbox.y1 - 10

            # 如果文本会超出顶部边界，则放到边界框内部
            if text_y < 20:
                text_y = bbox.y1 + 25

            # 绘制文本
            self.draw_text_with_background(img, label, (text_x, text_y), color)

            # 在边界框右下角显示尺寸信息
            width_px = bbox.x2 - bbox.x1
            height_px = bbox.y2 - bbox.y1
            size_text = f"{width_px}x{height_px}"

            size_x = bbox.x2 - 80
            size_y = bbox.y2 - 5

            self.draw_text_with_background(img, size_text, (size_x, size_y),
                                           color, font_scale=0.4)

        # 绘制统计信息
        stats_y = height - 60
        stats_text = [
            f"总边界框数: {len(valid_data)}",
            f"图像尺寸: {width}x{height}",
            f"平均框大小: {self._calculate_avg_bbox_size(valid_data)}"
        ]

        for i, stat in enumerate(stats_text):
            cv2.putText(img, stat, (20, stats_y + i * 20),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (100, 100, 100), 1)

        # 保存图片
        cv2.imwrite(output_path, img)
        print(f"可视化图片已保存到: {output_path}")

        return img

    def _calculate_avg_bbox_size(self, valid_data: List) -> str:
        """计算平均边界框尺寸"""
        if not valid_data:
            return "0x0"

        total_width = sum(bbox.x2 - bbox.x1 for _, bbox, _ in valid_data)
        total_height = sum(bbox.y2 - bbox.y1 for _, bbox, _ in valid_data)

        avg_width = total_width // len(valid_data)
        avg_height = total_height // len(valid_data)

        return f"{avg_width}x{avg_height}"

    def create_excel_layout_analysis(self, bboxes: List[BoundingBox], texts: List[str],
                                     output_path: str = "excel_layout_analysis.png"):
        """创建Excel布局分析图"""
        img = self.visualize_excel_bboxes(bboxes, texts, output_path)

        # 添加布局分析
        height, width = img.shape[:2]

        # 绘制网格线帮助分析布局
        grid_color = (200, 200, 200)
        grid_spacing = 50

        # 垂直网格线
        for x in range(0, width, grid_spacing):
            cv2.line(img, (x, 0), (x, height), grid_color, 1)

        # 水平网格线
        for y in range(0, height, grid_spacing):
            cv2.line(img, (0, y), (width, y), grid_color, 1)

        # 保存分析图
        analysis_path = output_path.replace('.png', '_with_grid.png')
        cv2.imwrite(analysis_path, img)
        print(f"布局分析图已保存到: {analysis_path}")

        return img


def create_sample_excel_data():
    """创建模拟Excel采集数据"""
    # 模拟Excel表格中的不同元素
    sample_bboxes = [
        BoundingBox(x1=50, y1=80, x2=200, y2=110),  # 表头
        BoundingBox(x1=250, y1=80, x2=400, y2=110),  # 表头
        BoundingBox(x1=450, y1=80, x2=600, y2=110),  # 表头
        BoundingBox(x1=50, y1=120, x2=200, y2=150),  # 数据行1
        BoundingBox(x1=250, y1=120, x2=400, y2=150),  # 数据行1
        BoundingBox(x1=450, y1=120, x2=600, y2=150),  # 数据行1
        BoundingBox(x1=50, y1=160, x2=200, y2=190),  # 数据行2
        BoundingBox(x1=250, y1=160, x2=400, y2=190),  # 数据行2
        BoundingBox(x1=450, y1=160, x2=600, y2=190),  # 数据行2
        BoundingBox(x1=50, y1=220, x2=600, y2=250),  # 合并单元格
    ]

    sample_texts = [
        "姓名", "部门", "薪资",
        "张三", "技术部", "8000",
        "李四", "销售部", "6500",
        "总计：3人"
    ]

    return sample_bboxes, sample_texts


# 使用示例
if __name__ == "__main__":
    # 创建可视化器
    visualizer = BBoxVisualizer()

    # 创建示例数据
    bboxes, texts = create_sample_excel_data()

    # 生成基础可视化
    img = visualizer.visualize_excel_bboxes(
        bboxes, texts,
        "excel_visualization.png"
    )

    # 生成带网格的布局分析
    visualizer.create_excel_layout_analysis(
        bboxes, texts,
        "excel_layout_analysis.png"
    )

    print("Excel边界框可视化完成！")