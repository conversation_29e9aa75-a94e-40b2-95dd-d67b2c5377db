import json
import os

import cv2
import numpy as np
from typing import List, Tuple, TypeVar

from PIL import ImageFont, ImageDraw, Image
from loguru import logger

from vision.core import ImageHand<PERSON>, DetectionResult
from settings import ROOT
from vision.core.data_types import T, BoundingBox


class Visualizer:
    """可视化器，负责结果绘制和导出"""

    def __init__(self, font_size: int = 20):
        self.font_size = font_size
        self._init_font()
        self.color_palette = None

    def _init_font(self):
        """初始化字体"""
        font_path = ROOT / 'fonts/simhei.ttf'
        try:
            self.font = ImageFont.truetype(str(font_path), self.font_size)
        except (OSError, IOError):
            logger.warning(f"无法加载字体文件：{font_path}，使用默认字体")
            self.font = ImageFont.load_default()

    def init_color_palette(self, max_class_id: int):
        """初始化颜色调色板"""
        np.random.seed(42)  # 固定随机种子，确保颜色一致性
        self.color_palette = np.random.uniform(0, 255, size=(max_class_id + 1, 3))

    def draw_detections(self, image: np.ndarray, detections: List[DetectionResult]) -> np.ndarray:
        """在图像上绘制检测结果"""
        if not detections:
            return image

        # 确保颜色调色板已初始化
        if self.color_palette is None:
            max_class_id = max(det.class_id for det in detections)
            self.init_color_palette(max_class_id)

        # 转换为PIL图像以支持中文显示
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_image)

        for detection in detections:
            # 获取颜色
            color = tuple(map(int, self.color_palette[detection.class_id % len(self.color_palette)]))

            # 绘制边界框
            draw.rectangle([detection.x1, detection.y1, detection.x2, detection.y2],
                           outline=color, width=2)

            # 绘制标签
            label = f"{detection.class_name}: {detection.confidence:.2f}"
            self._draw_label(draw, label, detection.x1, detection.y1, color)

        # 转换回OpenCV格式
        return cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)

    def draw_bboxes_with_texts(self, image: np.ndarray, bboxes: List[BoundingBox], texts: List[str] = None) -> np.ndarray:
        """绘制边界框，可选择在框内绘制文本"""
        if not bboxes:
            return image

        # 如果提供了texts，校验长度是否一致
        if texts is not None and len(texts) != len(bboxes):
            raise ValueError(f"texts长度({len(texts)})与bboxes长度({len(bboxes)})不一致")

        # 转换为PIL图像以支持中文显示
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_image)

        # 绘制边界框
        self._draw_bounding_boxes(draw, bboxes)

        # 如果提供了文本，绘制文本
        if texts is not None:
            self._draw_texts_in_boxes(draw, bboxes, texts)

        # 转换回OpenCV格式
        return cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)

    def _draw_bounding_boxes(self, draw: ImageDraw.Draw, bboxes: List[BoundingBox],
                             color: Tuple[int, int, int] = (255, 0, 0), width: int = 2):
        """专门负责绘制边界框"""
        for bbox in bboxes:
            # 跳过无效的边界框
            if any(coord is None for coord in [bbox.x1, bbox.y1, bbox.x2, bbox.y2]):
                continue

            # 绘制边界框
            draw.rectangle([bbox.x1, bbox.y1, bbox.x2, bbox.y2],
                           outline=color, width=width)

    def _draw_texts_in_boxes(self, draw: ImageDraw.Draw, bboxes: List[BoundingBox], texts: List[str]):
        """专门负责在边界框内绘制文本"""
        for i, (bbox, text) in enumerate(zip(bboxes, texts)):
            # 跳过无效的边界框或空文本
            if any(coord is None for coord in [bbox.x1, bbox.y1, bbox.x2, bbox.y2]) or not text:
                continue

            # 获取文本尺寸
            bbox_coords = draw.textbbox((0, 0), text, font=self.font)
            text_width = bbox_coords[2] - bbox_coords[0]
            text_height = bbox_coords[3] - bbox_coords[1]

            # 计算边界框的尺寸
            bbox_width = bbox.x2 - bbox.x1
            bbox_height = bbox.y2 - bbox.y1

            # 计算文本位置（居中）
            text_x = bbox.x1 + (bbox_width - text_width) // 2
            text_y = bbox.y1 + (bbox_height - text_height) // 2

            # 确保文本不超出边界框
            text_x = max(bbox.x1, min(text_x, bbox.x2 - text_width))
            text_y = max(bbox.y1, min(text_y, bbox.y2 - text_height))

            # 绘制文本背景（半透明白色）
            draw.rectangle([text_x - 2, text_y - 2, text_x + text_width + 2, text_y + text_height + 2],
                           fill=(255, 255, 255, 200))

            # 绘制文本（黑色）
            draw.text((text_x, text_y), text, fill=(0, 0, 0), font=self.font)

    def _draw_label(self, draw: ImageDraw.Draw, label: str, x: int, y: int,
                    color: Tuple[int, int, int]):
        """绘制标签文本"""
        # 获取文本尺寸
        bbox = draw.textbbox((0, 0), label, font=self.font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        # 计算标签位置
        label_y = max(0, y - text_height - 5)

        # 绘制背景
        draw.rectangle([x, label_y, x + text_width + 4, label_y + text_height + 4], fill=color)

        # 绘制文本
        draw.text((x + 2, label_y + 2), label, fill=(0, 0, 0), font=self.font)


def crop_bboxes(image_input, bboxes) -> Tuple[List[np.ndarray], dict]:
    """
    根据 bbox 裁剪图像
    image_input: 图像对象
    bbox: [(x1, y1, x2, y2)]
    """
    bgr_image, source_info = ImageHandler.normalize_input(image_input)

    cropped_images = []
    for (x1, y1, x2, y2) in bboxes:
        # 确保坐标在图像范围内
        x1 = max(0, int(x1))
        y1 = max(0, int(y1))
        x2 = min(bgr_image.shape[1], int(x2))
        y2 = min(bgr_image.shape[0], int(y2))

        if x2 > x1 and y2 > y1:
            cropped = bgr_image[y1:y2, x1:x2]
            cropped_images.append(cropped)

    return cropped_images, source_info


def calculate_iou(boxA, boxB):
    """
    计算两个 bbox 的 IoU
    boxA, boxB: (x1, y1, x2, y2)
    """
    # 计算交集坐标
    x_left   = max(boxA[0], boxB[0])
    y_top    = max(boxA[1], boxB[1])
    x_right  = min(boxA[2], boxB[2])
    y_bottom = min(boxA[3], boxB[3])

    # 没有交集
    if x_right <= x_left or y_bottom <= y_top:
        return 0.0

    # 交集面积
    inter_area = (x_right - x_left) * (y_bottom - y_top)

    # 各自面积
    areaA = (boxA[2] - boxA[0]) * (boxA[3] - boxA[1])
    areaB = (boxB[2] - boxB[0]) * (boxB[3] - boxB[1])

    # 并集面积
    union_area = areaA + areaB - inter_area

    # IoU
    iou = inter_area / union_area
    return iou


def intersection_area(boxA, boxB):
    # 计算交集面积
    x1 = max(boxA[0], boxB[0])
    y1 = max(boxA[1], boxB[1])
    x2 = min(boxA[2], boxB[2])
    y2 = min(boxA[3], boxB[3])

    if x2 <= x1 or y2 <= y1:
        return 0
    return (x2 - x1) * (y2 - y1)


def bbox_overlap_ratio(bbox1, bbox2):
    """
    计算 bbox1 在 bbox2 内的比例
    :param bbox1: (x1, y1, x2, y2)
    :param bbox2: (x1, y1, x2, y2)
    :return: ratio (0~1)
    """
    x1 = max(bbox1[0], bbox2[0])
    y1 = max(bbox1[1], bbox2[1])
    x2 = min(bbox1[2], bbox2[2])
    y2 = min(bbox1[3], bbox2[3])

    # 没有交集
    if x2 <= x1 or y2 <= y1:
        return 0.0

    inter_area = (x2 - x1) * (y2 - y1)   # 交集面积
    bbox1_area = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])  # bbox1 面积

    return inter_area / bbox1_area


def non_maximum_suppression(detections: List[T], iou_threshold: float = 0.1) -> List[T]:
    """
    对检测结果应用非极大值抑制(NMS)算法，过滤重叠的边界框
    :param detections: pair检测结果列表
    :param iou_threshold: IOU阈值，用于判断边界框重叠程度，默认0.5
    :return: 经过NMS处理后的检测结果列表
    """
    if len(detections) <= 1:
        return detections

    # 准备NMS输入数据
    boxes = []
    scores = []

    for detection in detections:
        x, y, width, height = detection.xywh
        boxes.append([x, y, width, height])
        scores.append(detection.confidence if detection.confidence is not None else 0.3)

    # 应用OpenCV的NMS算法
    try:
        indices = cv2.dnn.NMSBoxes(
            boxes,
            scores,
            score_threshold=0.0,  # 不进行置信度过滤，因为已经在前面过滤过了
            nms_threshold=iou_threshold
        )

        # 处理NMS返回结果
        if indices is not None and len(indices) > 0:
            # OpenCV 4.x返回的是numpy数组，需要flatten
            if hasattr(indices, 'flatten'):
                indices = indices.flatten()
            elif not isinstance(indices, (list, tuple)):
                indices = [indices]

            # 根据保留的索引构建结果列表
            filtered_detections = [detections[i] for i in indices]
            return filtered_detections
        else:
            logger.warning("NMS处理后没有保留任何检测框")
            return []

    except Exception as e:
        logger.exception(f"NMS处理失败: {e}")
        return detections


def save_bbox_crops(image_input, bboxes: List[T], folder_path: str, prefix: str = "crop"):
    """
    将 BoundingBox 对应的图像区域裁剪并保存到指定文件夹

    :param image_input: 图像输入
    :param bboxes: BoundingBox 列表
    :param folder_path: 保存裁剪图像的文件夹
    :param prefix: 保存文件的前缀
    """
    os.makedirs(folder_path, exist_ok=True)

    image, _ = ImageHandler.normalize_input(image_input)
    for idx, bbox in enumerate(bboxes, start=1):
        crop = ImageHandler.crop_bbox(image, bbox)
        file_path = os.path.join(folder_path, f"{prefix}_{idx}.png")
        cv2.imwrite(file_path, crop)

    logger.info(f"{len(bboxes)} crops saved to {folder_path}")
