"""
核心数据类型定义
"""
import math
from dataclasses import dataclass
from uuid import uuid4

from pydantic import BaseModel, Field, ConfigDict
from typing import Dict, Any, List, Union, Optional, Tuple, TypeVar
import numpy as np

T = TypeVar('T', bound='BoundingBox')


def to_camel_case(string: str) -> str:
    """将下划线命名转换为驼峰命名"""
    components = string.split('_')
    return components[0] + ''.join(word.capitalize() for word in components[1:])


class CamelCaseModel(BaseModel):
    """支持驼峰命名的基础模型"""
    model_config = ConfigDict(
        alias_generator=to_camel_case,
        populate_by_name=True,  # 允许使用原始字段名和别名
        from_attributes=True  # 支持从ORM对象创建
    )


@dataclass
class ImageMeta:
    """图像元数据"""
    source_type: str  # 'path', 'pil', 'numpy', 'url', 'base64'
    source: str
    original_shape: Tuple[int, int]  # (height, width)
    processed_shape: Tuple[int, int]
    global_index: int
    batch_index: int


@dataclass
class BatchPreprocessResult:
    """批量预处理结果"""
    batch_data: np.ndarray  # (batch_size, 3, H, W)
    image_metas: List[ImageMeta]
    valid_indices: List[int]


class ClassificationResult(CamelCaseModel):
    """分类结果"""
    class_id: int
    class_name: str
    confidence: float
    top_k_predictions: List[Dict[str, Union[int, str, float]]]
    image_index: int
    processing_time: float

class Point(CamelCaseModel):
    x: float
    y: float

class BoundingBox(CamelCaseModel):
    x1: Optional[int] = None
    y1: Optional[int] = None
    x2: Optional[int] = None
    y2: Optional[int] = None

    @staticmethod
    def from_array(array: list) -> 'BoundingBox':
        """
        从一个包含四个元素的列表创建 BoundingBox 对象。

        参数:
        - array: 包含 [x1, y1, x2, y2] 的列表

        返回:
        - BoundingBox 对象
        """
        if len(array) != 4:
            return BoundingBox(x1=-1, y1=-1, x2=-1, y2=-1)
        x1, y1, x2, y2 = array
        x1, y1, x2, y2 = map(int, (x1, y1, x2, y2))
        return BoundingBox(x1=x1, y1=y1, x2=x2, y2=y2)

    @staticmethod
    def from_corner4(corner4: List[Tuple[int, int]]) -> 'BoundingBox':
        """
        从四个角点坐标创建 BoundingBox 对象。

        参数:
        - corner4: 包含四个点坐标的列表 [(x1, y1), (x2, y1), (x2, y2), (x1, y2)]

        返回:
        - BoundingBox 对象
        """
        if len(corner4) != 4:
            return BoundingBox(x1=-1, y1=-1, x2=-1, y2=-1)
        xs = [pt[0] for pt in corner4]
        ys = [pt[1] for pt in corner4]
        x1, y1, x2, y2 = min(xs), min(ys), max(xs), max(ys)
        return BoundingBox(x1=x1, y1=y1, x2=x2, y2=y2)

    def min_distance(self, other: "BoundingBox") -> float:
        """
        计算两个 bbox 的最短距离
        """
        x1, y1, x2, y2 = self.xyxy
        ox1, oy1, ox2, oy2 = other.xyxy

        # 横向距离
        if x2 < ox1:  # self 在 other 左边
            dx = ox1 - x2
        elif ox2 < x1:  # self 在 other 右边
            dx = x1 - ox2
        else:
            dx = 0  # 水平方向有重叠

        # 纵向距离
        if y2 < oy1:  # self 在 other 上方
            dy = oy1 - y2
        elif oy2 < y1:  # self 在 other 下方
            dy = y1 - oy2
        else:
            dy = 0  # 垂直方向有重叠

        return math.hypot(dx, dy)  # sqrt(dx^2 + dy^2)

    @property
    def x_min(self) -> int:
        """
        获取边界框的最小 x 坐标。
        """
        return min(self.x1, self.x2)

    @property
    def x_max(self) -> int:
        """
        获取边界框的最大 x 坐标。
        """
        return max(self.x1, self.x2)

    @property
    def y_min(self) -> int:
        """
        获取边界框的最小 y 坐标。
        """
        return min(self.y1, self.y2)

    @property
    def y_max(self) -> int:
        """
        获取边界框的最大 y 坐标。
        """
        return max(self.y1, self.y2)

    @property
    def center(self):
        """
        获取边界框的中心点坐标。
        """
        return (self.x1 + self.x2) / 2, (self.y1 + self.y2) / 2

    @property
    def width(self):
        """
        获取边界框的宽度。
        """
        return abs(self.x2 - self.x1)

    @property
    def height(self):
        """
        获取边界框的高度。
        """
        return abs(self.y2 - self.y1)

    @property
    def bbox(self) -> Dict[str, int]:
        return {
            "x": min(self.x1, self.x2),
            "y": min(self.y1, self.y2),
            "x1": self.x1, "y1": self.y1,
            "x2": self.x2, "y2": self.y2,
            "width": self.width,
            "height": self.height
        }

    @property
    def xyxy(self) -> Tuple[int, int, int, int]:
        """
        获取检测结果的左上角和右下角坐标。

        返回：
        - (x1, y1, x2, y2)
        """
        x1 = min(self.x1, self.x2)
        y1 = min(self.y1, self.y2)
        x2 = max(self.x1, self.x2)
        y2 = max(self.y1, self.y2)
        return x1, y1, x2, y2

    def xyxy_rel(self, width, height) -> Tuple[float, float, float, float]:
        """
        获取检测结果的相对坐标（归一化到0-1范围）。

        返回：
        - (x1, y1, x2, y2)
        """
        x1, y1, x2, y2 = self.xyxy
        return (
            x1 / width,
            y1 / height,
            x2 / width,
            y2 / height
        )

    def xywh_rel(self, width, height) -> Tuple[float, float, float, float]:
        """
        获取检测结果的中心点坐标和宽高（归一化到0-1范围）。

        返回：
        - (x, y, width, height)
        """
        x, y, w, h = self.xywh
        return (
            x / width,
            y / height,
            w / width,
            h / height
        )

    @property
    def xywh(self) -> Tuple[int, int, int, int]:
        """
        获取检测结果的中心点坐标和宽高。

        返回：
        - (x, y, width, height)
        """
        x = min(self.x1, self.x2)
        y = min(self.y1, self.y2)
        width = abs(self.x2 - self.x1)
        height = abs(self.y2 - self.y1)
        return x, y, width, height

    @property
    def area(self) -> int:
        """
        计算边界框的面积。

        返回：
        - 面积值
        """
        width = abs(self.x2 - self.x1)
        height = abs(self.y2 - self.y1)
        return width * height

    def map_to_original(self, crop_bbox: T) -> T:
        """
        将当前 BoundingBox（相对于裁剪图像的绝对坐标）映射到原图的绝对坐标。

        参数:
        - crop_bbox: BoundingBox，表示当前图像是从原图中哪个区域裁剪出来的

        返回：
        - DetectionResult：映射回原图的新检测结果
        """
        origin_x, origin_y, _, _ = crop_bbox.xyxy

        new_item = self.model_copy()
        x1, y1, x2, y2 = new_item.xyxy
        new_item.x1 = x1 + origin_x
        new_item.y1 = y1 + origin_y
        new_item.x2 = x2 + origin_x
        new_item.y2 = y2 + origin_y

        return new_item

    def y_overlap_ratio(self, other: 'BoundingBox') -> float:
        """
        计算两个 OCR 框在 y 轴上的重合比例
        返回值范围 [0, 1]，越大说明越重合
        """
        overlap = max(0, min(self.y_max, other.y_max) - max(self.y_min, other.y_min))
        height_a = self.y_max - self.y_min
        height_b = other.y_max - other.y_min
        return overlap / max(height_a, height_b)

    @staticmethod
    def group_bboxes_by_lines(bboxes: List[T], overlap_threshold: float = 0.8) -> List[List[T]]:
        """
        根据 y 轴重叠率分行
        """
        if not bboxes:
            return []

        # 先按照 y_min 排序（行的起点），再按照 x_min 作为辅助排序
        sorted_bboxes = sorted(bboxes, key=lambda b: (b.y_min, b.x_min))

        lines: List[List[T]] = []
        for bbox in sorted_bboxes:
            placed = False
            for line in lines:
                # 判断是否和当前行的第一个框属于同一行
                if bbox.y_overlap_ratio(line[0]) >= overlap_threshold:
                    line.append(bbox)
                    placed = True
                    break
            if not placed:
                lines.append([bbox])

        # 每行内部按 x_min 排序
        for line in lines:
            line.sort(key=lambda b: b.x_min)

        return lines

    @staticmethod
    def sort_bboxes(bboxes: List[T], overlap_threshold: float = 0.8) -> List[T]:
        """
        一行一行，从左到右、从上到下排序
        """
        lines = BoundingBox.group_bboxes_by_lines(bboxes, overlap_threshold)
        # 展平
        return [bbox for line in lines for bbox in line]

    @staticmethod
    def xyxy_to_corner4(xyxy: Tuple[int, int, int, int]) -> List[Tuple[int, int]]:
        """
        将 (x1, y1, x2, y2) 转换为四个角点坐标 [(x1, y1), (x2, y1), (x2, y2), (x1, y2)]
        """
        x1, y1, x2, y2 = xyxy
        x1 = min(x1, x2)
        y1 = min(y1, y2)
        x2 = max(x1, x2)
        y2 = max(y1, y2)
        return [(x1, y1), (x2, y1), (x2, y2), (x1, y2)]

    @staticmethod
    def corner4_to_xyxy(corner4: List[Tuple[int, int]]) -> Tuple[int, int, int, int]:
        """
        将四个角点坐标 [(x1, y1), (x2, y1), (x2, y2), (x1, y2)] 转换为 (x1, y1, x2, y2)
        """
        if len(corner4) != 4:
            raise ValueError("corner4 必须包含四个点")
        xs = [pt[0] for pt in corner4]
        ys = [pt[1] for pt in corner4]
        return min(xs), min(ys), max(xs), max(ys)


class OcrItem(BoundingBox):
    text: str
    confidence: float

class Difficult(CamelCaseModel):
    origin_text: Optional[str] = None
    optimized_text: Optional[str] = None
    notes: Optional[str] = None


class DetectionResult(BoundingBox):
    """检测结果"""
    id: str = Field(default_factory=lambda: str(uuid4()))
    class_id: Optional[int] = None
    class_name: Optional[str] = None
    confidence: Optional[float] = None
    image_index: Optional[int] = None
    original_text: Optional[str] = None
    difficult: Optional[Difficult] = None


class BatchResult(CamelCaseModel):
    """批量处理结果基类"""
    processing_time: float
    batch_size: int
    successful_count: int
    failed_indices: List[int]


class BatchClassificationResult(BatchResult):
    """批量分类结果"""
    results: List[Optional[ClassificationResult]]


class BatchDetectionResult(BatchResult):
    """批量检测结果"""
    results: List[List[DetectionResult]]