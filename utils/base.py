import time


def singleton(cls):
    _instances = {}

    def wrapper(*args, **kwargs):
        if cls not in _instances:
            _instances[cls] = cls(*args, **kwargs)
        return _instances[cls]
    return wrapper


def format_time(t: float = None, fmt: str = '%Y-%m-%d %H:%M:%S'):
    t = time.localtime(time.time() if t is None else t)
    return time.strftime(fmt, t)


def format_elapsed(t: float, use_second: bool = True):
    # assert t > 0, 'The time cannot be empty.'
    result = ''
    d, h, m, s = int(t // (3600 * 24)), int(t % (3600 * 24) // 3600), int(t % 3600 // 60), int(t % 60)
    if d > 0:
        result += f'{d} 天 '
    if h > 0:
        result += f'{h} 小时 '
    if m > 0:
        result += f'{m} 分钟 '
    if use_second or {d, h, m}.issubset({0}):
        if s > 0:
            result += f'{s} 秒 '
        else:
            result += f'{int(t * 1000)} 毫秒 '
    return result.strip()
