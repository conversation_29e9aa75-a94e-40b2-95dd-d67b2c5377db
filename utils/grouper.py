from typing import List, TypeVar, Callable, Any
import statistics

T = TypeVar('T')


class Grouper:
    """
    通用分组器，使用统计学方法将对象根据数值差异进行分组
    """

    def __init__(self, tolerance: float = 0.3):
        """
        初始化分组器

        Args:
            tolerance: 容差值，差异在此范围内的对象将被归为一组
        """
        self.tolerance = tolerance

    def simple_group(self, data: List[T], value_func: Callable[[T], float]) -> List[List[int]]:
            """
            对数据进行分组

            Args:
                data: 待分组的数据列表
                value_func: 将对象转换为数值的lambda函数

            Returns:
                List[List[int]]: 分组索引列表，每个子列表包含属于同一组的元素索引
            """
            if not data:
                return []

            # 计算每个元素的数值
            values = []
            for item in data:
                values.append(value_func(item))

            # 使用并查集进行分组
            groups = []
            assigned = [False] * len(data)

            for i in range(len(data)):
                if assigned[i]:
                    continue

                # 创建新组
                current_group = [i]
                assigned[i] = True

                # 查找相似数值的元素
                for j in range(i + 1, len(data)):
                    if assigned[j]:
                        continue

                    # 计算数值比例
                    if values[j] == 0 and values[i] == 0:
                        # 两个值都为0，认为相似
                        ratio = 1.0
                    elif values[j] == 0 or values[i] == 0:
                        # 其中一个为0，另一个不为0，认为不相似
                        ratio = 0.0
                    else:
                        # 计算比例，取较小的值
                        ratio = min(values[i] / values[j], values[j] / values[i])

                    # 如果比例在容忍范围内，加入同一组
                    if ratio >= (1 - self.tolerance):
                        current_group.append(j)
                        assigned[j] = True

                groups.append(current_group)

            # 按组大小排序，优先处理较大的组
            groups.sort(key=len, reverse=True)
            return groups

    def group(self, data: List[T], value_func: Callable[[T], float]) -> List[List[int]]:
        """
        对数据进行分组

        Args:
            data: 待分组的数据列表
            value_func: 将对象转换为数值的lambda函数

        Returns:
            List[List[int]]: 分组索引列表，每个子列表包含属于同一组的元素索引
        """
        if not data:
            return []

        # 提取数值并保持索引对应关系
        indexed_values = [(i, value_func(item)) for i, item in enumerate(data)]

        # 按数值排序
        indexed_values.sort(key=lambda x: x[1])

        groups = []
        current_group = [indexed_values[0][0]]  # 从第一个元素开始
        current_group_values = [indexed_values[0][1]]

        for i in range(1, len(indexed_values)):
            index, value = indexed_values[i]

            # 计算当前值与当前组的统计距离
            if self._should_add_to_group(value, current_group_values):
                current_group.append(index)
                current_group_values.append(value)
            else:
                # 开始新组
                groups.append(current_group)
                current_group = [index]
                current_group_values = [value]

        # 添加最后一组
        groups.append(current_group)

        return groups

    def _should_add_to_group(self, value: float, group_values: List[float]) -> bool:
        """
        判断一个值是否应该加入当前组

        使用多种统计学方法判断：
        1. 与组内平均值的差异
        2. 与组内最近值的差异
        3. 考虑组内的标准差
        """
        if not group_values:
            return True

        # 方法1: 与组内平均值的差异
        group_mean = statistics.mean(group_values)
        mean_diff = abs(value - group_mean)

        # 方法2: 与组内最近值的差异（排序后的相邻值）
        closest_diff = min(abs(value - gv) for gv in group_values)

        # 方法3: 考虑组内标准差（如果组内有多个值）
        if len(group_values) > 1:
            group_std = statistics.stdev(group_values)
            # 如果新值在平均值±(标准差+容差)范围内，则加入组
            std_based_tolerance = group_std + self.tolerance
            return mean_diff <= std_based_tolerance
        else:
            # 如果组内只有一个值，直接比较差异
            return closest_diff <= self.tolerance

    def group_with_advanced_stats(self, data: List[T], value_func: Callable[[T], float]) -> List[List[int]]:
        """
        使用更高级的统计学方法进行分组（聚类算法）

        Args:
            data: 待分组的数据列表
            value_func: 将对象转换为数值的lambda函数

        Returns:
            List[List[int]]: 分组索引列表
        """
        if not data:
            return []

        # 提取数值并保持索引对应关系
        indexed_values = [(i, value_func(item)) for i, item in enumerate(data)]
        values = [v[1] for v in indexed_values]

        # 使用简单的层次聚类方法
        clusters = [[i] for i in range(len(values))]

        while True:
            min_distance = float('inf')
            merge_indices = None

            # 找到最近的两个聚类
            for i in range(len(clusters)):
                for j in range(i + 1, len(clusters)):
                    distance = self._cluster_distance(
                        [values[idx] for idx in clusters[i]],
                        [values[idx] for idx in clusters[j]]
                    )
                    if distance < min_distance:
                        min_distance = distance
                        merge_indices = (i, j)

            # 如果最小距离超过容差，停止合并
            if min_distance > self.tolerance:
                break

            # 合并聚类
            i, j = merge_indices
            clusters[i].extend(clusters[j])
            clusters.pop(j)

        # 将内部索引转换为原始索引
        result = []
        for cluster in clusters:
            original_indices = [indexed_values[idx][0] for idx in cluster]
            result.append(original_indices)

        return result

    def _cluster_distance(self, cluster1: List[float], cluster2: List[float]) -> float:
        """
        计算两个聚类之间的距离（使用组间平均距离）
        """
        if not cluster1 or not cluster2:
            return float('inf')

        mean1 = statistics.mean(cluster1)
        mean2 = statistics.mean(cluster2)
        return abs(mean1 - mean2)


# 使用示例和测试
if __name__ == "__main__":
    # 示例1: 数值列表分组
    numbers = [1.0, 1.1, 1.2, 5.0, 5.1, 10.0, 10.5, 11.0]
    grouper = Grouper(tolerance=1.0)

    # 直接对数值进行分组
    groups = grouper.group(numbers, lambda x: x)
    print("数值分组结果:", groups)


    # 示例2: 对象列表分组
    class Point:
        def __init__(self, x, y):
            self.x = x
            self.y = y

        def __repr__(self):
            return f"Point({self.x}, {self.y})"


    points = [
        Point(1, 2), Point(1.1, 2.1), Point(1.2, 2.2),  # 第一组
        Point(5, 6), Point(5.1, 6.1),  # 第二组
        Point(10, 11), Point(10.5, 11.5), Point(11, 12)  # 第三组
    ]

    # 按x坐标分组
    x_groups = grouper.group(points, lambda p: p.x)
    print("按x坐标分组:", x_groups)

    # 按欧几里得距离分组
    import math

    distance_groups = grouper.group(points, lambda p: math.sqrt(p.x ** 2 + p.y ** 2))
    print("按距离原点分组:", distance_groups)

    # 示例3: 使用高级统计方法
    advanced_groups = grouper.group_with_advanced_stats(numbers, lambda x: x)
    print("高级统计分组结果:", advanced_groups)