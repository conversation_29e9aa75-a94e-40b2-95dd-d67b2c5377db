#!/usr/bin/env python3
"""
简单的语法检查脚本
"""

import ast
import sys

def check_syntax(filename):
    """检查Python文件的语法"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 尝试解析AST
        ast.parse(source)
        print(f"✓ {filename} 语法检查通过")
        return True
        
    except SyntaxError as e:
        print(f"✗ {filename} 语法错误: {e}")
        print(f"  行号: {e.lineno}")
        print(f"  位置: {e.offset}")
        return False
        
    except Exception as e:
        print(f"✗ {filename} 检查失败: {e}")
        return False

if __name__ == "__main__":
    files_to_check = [
        "api/invest_routes.py"
    ]
    
    print("开始语法检查...")
    print("=" * 40)
    
    all_passed = True
    for filename in files_to_check:
        if not check_syntax(filename):
            all_passed = False
    
    print("=" * 40)
    if all_passed:
        print("✓ 所有文件语法检查通过")
    else:
        print("✗ 存在语法错误")
        sys.exit(1)
