#!/usr/bin/env python3
"""
测试 invest_routes 中的审查分层信息功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from api.invest_routes import DrillHoleData, StratificationReviewResult
    print("✓ 成功导入 invest_routes 模块")
except ImportError as e:
    print(f"✗ 导入 invest_routes 模块失败: {e}")
    sys.exit(1)

from collections import defaultdict

def test_drill_hole_data():
    """测试钻孔数据结构"""
    print("测试钻孔数据结构...")
    
    drill_data = DrillHoleData("ZK001", "钻孔柱状图")
    drill_data.surface_elevation = 15.5
    drill_data.depth = 20.0
    drill_data.coordinates = {"x": 100.0, "y": 200.0}
    
    print(f"钻孔ID: {drill_data.drill_id}")
    print(f"数据源: {drill_data.source_type}")
    print(f"表面标高: {drill_data.surface_elevation}")
    print(f"深度: {drill_data.depth}")
    print(f"坐标: {drill_data.coordinates}")
    print("✓ 钻孔数据结构测试通过")

def test_stratification_review_result():
    """测试分层信息审查结果结构"""
    print("\n测试分层信息审查结果结构...")
    
    review_result = StratificationReviewResult()
    
    # 添加测试数据
    drill_data1 = DrillHoleData("ZK001", "钻孔柱状图")
    drill_data1.surface_elevation = 15.5
    
    drill_data2 = DrillHoleData("ZK001", "剖面图")
    drill_data2.surface_elevation = 15.6
    
    review_result.drill_holes["ZK001"].append(drill_data1)
    review_result.drill_holes["ZK001"].append(drill_data2)
    
    print(f"钻孔数量: {len(review_result.drill_holes)}")
    print(f"ZK001 数据源数量: {len(review_result.drill_holes['ZK001'])}")
    print("✓ 分层信息审查结果结构测试通过")

def create_mock_data():
    """创建模拟数据用于测试"""
    print("\n创建模拟数据...")

    logic_result_map = defaultdict(list)
    print(f"创建了 {len(logic_result_map)} 种类型的模拟数据")
    return logic_result_map

def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")

    try:
        logic_result_map = create_mock_data()
        print("✓ 基本功能测试通过")
        return True

    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试 invest_routes 审查分层信息功能")
    print("=" * 50)

    # 运行测试
    test_drill_hole_data()
    test_stratification_review_result()
    test_basic_functionality()

    print("\n" + "=" * 50)
    print("所有测试完成")
