from datetime import datetime
from urllib.request import Request
from fastapi import HTT<PERSON><PERSON>xception
from fastapi.staticfiles import StaticFiles
from fastapi_offline import FastAPIOffline
from starlette.responses import JSONResponse, Response
from starlette.exceptions import HTTPException as StarletteHTTPException

from api.base import Config, ApiResponse
from api import yolo_routes, ocr_routes, classifier_routes, pdf_routes, repair_routes, invest_routes
from utils.route import create_detection_router
from drawing_service import LegendVisionComputer, SectionVisionComputer, PlaneVisionComputer, XTableVisionComputer, MisalignedTableVisionComputer
from settings import ROOT


async def http_exception_handler(request: Request, exc: HTTPException) -> Response:
    """统一异常处理器 - HTTP异常"""
    return JSONResponse(
        status_code=exc.status_code,
        content=ApiResponse(
            success=False,
            data=None,
            message=exc.detail,
            error_code=f"HTTP_{exc.status_code}",
            timestamp=datetime.now().isoformat()
        ).model_dump()
    )

async def general_exception_handler(request: Request, exc: Exception) -> Response:
    """通用异常处理器 - 其他异常"""
    return JSONResponse(
        status_code=500,
        content=ApiResponse(
            success=False,
            data=None,
            message="服务器内部错误",
            error_code="INTERNAL_ERROR",
            timestamp=datetime.now().isoformat()
        ).model_dump()
    )


def create_app():
    app = FastAPIOffline(
        title="图纸智能审查",
        description="基于计算机视觉的图纸智能审查API",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )

    app.add_exception_handler(HTTPException, http_exception_handler)  # type: ignore
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)  # type: ignore
    app.add_exception_handler(Exception, general_exception_handler)

    @app.get("/")
    async def root():
        """根路径"""
        return {
            "message": "欢迎使用图纸智能审查API",
            "version": "1.0.0",
            "docs": "/docs"
        }

    # 挂载静态文件
    app.mount("/static", StaticFiles(directory=ROOT / Config.OUTPUT_DIR), name="static")

    app.include_router(ocr_routes.router)
    app.include_router(yolo_routes.router)
    app.include_router(classifier_routes.router)
    app.include_router(pdf_routes.router)
    app.include_router(repair_routes.router)
    app.include_router(invest_routes.router)

    # 批量注册检测器接口
    detectors = [
        (LegendVisionComputer, "/legend", "图例检测 API"),
        (PlaneVisionComputer, "/plane", "平面图检测 API"),
        (SectionVisionComputer, "/section", "剖面图检测 API"),
        (XTableVisionComputer, "/xtable", "错位表格检测 API"),
        (MisalignedTableVisionComputer, "/misaligned_table", "快速错位表格检测 API"),
    ]

    for detector_cls, prefix, tag in detectors:
        app.include_router(create_detection_router(detector_cls, prefix, tag))

    return app
