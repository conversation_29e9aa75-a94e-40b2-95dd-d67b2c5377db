from collections import defaultdict
from typing import List, Union, Dict, Any, Optional
from fastapi import APIRouter, Body
from api.base import ApiResponse
from models.legend_models import DetectedLegendResult
from models.misaligned_table_models import MisalignedTableResult, TableInfo
from models.pdf_page import PdfPageType
from models.plane_models import DetectedPlainResult
from models.section_models import DetectedSectionResult, DrillHole
from models.sheet_models import DetectedTableResult
from drawing_service.stratification_review_service import StratificationReviewService

router = APIRouter(prefix="/invest", tags=["审查报告相关 API"])

LogicResult = Union[
    DetectedLegendResult,
    MisalignedTableResult,
    DetectedPlainResult,
    DetectedSectionResult,
    DetectedTableResult,
]


@router.post("/invest_report")
async def invest_report(
    logic_results: List[LogicResult] = Body(...)
):
    """
    对逻辑结果进行修补
    """
    logic_result_map = defaultdict(list)
    for logic_result in logic_results:
        logic_result_map[logic_result.detect_type].append(logic_result)

    # 审查钻口信息
    # 审查勘探孔间距


    # 审查分层信息
    stratification_review_service = StratificationReviewService()
    stratification_review_result = await stratification_review_service.review_stratification_info(logic_result_map)

    # 返回数据结构待定
    return ApiResponse.success_response(data={
        "stratification_review": stratification_review_result,
        "original_results": logic_results
    })