#!/usr/bin/env python3
"""
简化的分层信息审查功能测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    try:
        from drawing_service.stratification_review_service import (
            StratificationReviewService,
            DrillHoleData,
            StratificationReviewResult
        )
        print("✓ 成功导入核心类")
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        from drawing_service.stratification_review_service import (
            StratificationReviewService,
            DrillHoleData,
            StratificationReviewResult
        )
        
        # 测试数据结构创建
        drill_data = DrillHoleData("ZK001", "测试数据源")
        drill_data.depth = 5.0
        drill_data.elevation = 10.0
        
        review_result = StratificationReviewResult()
        review_result.drill_holes["ZK001"].append(drill_data)
        
        print("✓ 基本数据结构创建成功")
        print(f"  钻孔ID: {drill_data.drill_id}")
        print(f"  数据源: {drill_data.source_type}")
        print(f"  深度: {drill_data.depth}")
        print(f"  标高: {drill_data.elevation}")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def test_service_creation():
    """测试服务类创建"""
    try:
        from drawing_service.stratification_review_service import StratificationReviewService
        
        service = StratificationReviewService()
        print("✓ 服务类创建成功")
        
        # 测试一些基本方法
        if hasattr(service, '_extract_drill_id_from_title'):
            print("✓ 包含钻孔号提取方法")
        
        if hasattr(service, '_find_table_header'):
            print("✓ 包含表头查找方法")
        
        if hasattr(service, '_perform_data_comparison'):
            print("✓ 包含数据对比方法")
        
        return True
        
    except Exception as e:
        print(f"✗ 服务类创建失败: {e}")
        return False

def test_test_data_creation():
    """测试测试数据创建"""
    try:
        from drawing_service.stratification_review_service import StratificationTestData
        from vision.core.data_types import DetectionResult
        
        # 测试钻孔柱状图测试数据
        drill_bar_data = StratificationTestData.create_drill_bar_chart_test_data()
        print(f"✓ 钻孔柱状图测试数据创建成功，包含 {len(drill_bar_data)} 个检测框")
        
        # 测试静力触探测试成果图表数据
        static_chart_data = StratificationTestData.create_static_penetration_chart_test_data()
        print(f"✓ 静力触探测试成果图表数据创建成功，包含 {len(static_chart_data)} 个检测框")
        
        # 测试静力触探分层参数表数据
        static_param_data = StratificationTestData.create_static_penetration_param_test_data()
        print(f"✓ 静力触探分层参数表数据创建成功，包含 {len(static_param_data)} 个检测框")
        
        # 验证数据结构
        for i, detection in enumerate(drill_bar_data[:3]):  # 只检查前3个
            print(f"  检测框{i+1}: 位置({detection.x1},{detection.y1},{detection.x2},{detection.y2}) 文本:'{detection.original_text}'")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试数据创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始简化测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("基本功能", test_basic_functionality),
        ("服务类创建", test_service_creation),
        ("测试数据创建", test_test_data_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"通过: {passed}/{total}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有基本测试通过！")
        return True
    else:
        print("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
