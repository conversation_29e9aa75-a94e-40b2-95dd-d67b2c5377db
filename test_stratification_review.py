#!/usr/bin/env python3
"""
分层信息审查功能测试脚本
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from drawing_service.stratification_review_service import (
        StratificationReviewService,
        StratificationTestRunner,
        StratificationTestData,
        StratificationTestVisualizer,
        run_stratification_tests
    )
    print("✓ 成功导入分层信息审查服务模块")
except ImportError as e:
    print(f"✗ 导入模块失败: {e}")
    sys.exit(1)


async def test_drill_id_extraction():
    """测试钻孔号提取功能"""
    print("\n" + "="*50)
    print("测试钻孔号提取功能")
    print("="*50)
    
    service = StratificationReviewService()
    
    # 测试静力触探测试成果图表的孔号提取
    test_data = StratificationTestData.create_static_penetration_chart_test_data()
    
    print("测试数据:")
    for i, detection in enumerate(test_data):
        print(f"  {i+1}. 位置({detection.x1},{detection.y1},{detection.x2},{detection.y2}) 文本: '{detection.original_text}'")
    
    # 测试位置关系提取
    drill_id = service._extract_drill_id_by_position(test_data)
    print(f"\n通过位置关系提取的钻孔号: {drill_id}")
    
    # 测试内容提取
    drill_id_content = service._extract_drill_id_from_content(test_data)
    print(f"通过内容提取的钻孔号: {drill_id_content}")
    
    return drill_id is not None


async def test_table_type_identification():
    """测试表格类型识别功能"""
    print("\n" + "="*50)
    print("测试表格类型识别功能")
    print("="*50)
    
    service = StratificationReviewService()
    
    # 测试三种表格类型
    test_cases = [
        ("钻孔柱状图", StratificationTestData.create_drill_bar_chart_test_data()),
        ("静力触探测试成果图表", StratificationTestData.create_static_penetration_chart_test_data()),
        ("静力触探分层参数表", StratificationTestData.create_static_penetration_param_test_data())
    ]
    
    results = []
    for expected_type, test_data in test_cases:
        identified_type = await service._identify_table_type(test_data)
        success = expected_type in identified_type
        results.append(success)
        
        print(f"期望类型: {expected_type}")
        print(f"识别类型: {identified_type}")
        print(f"识别结果: {'✓ 成功' if success else '✗ 失败'}")
        print("-" * 30)
    
    return all(results)


async def test_data_extraction():
    """测试数据提取功能"""
    print("\n" + "="*50)
    print("测试数据提取功能")
    print("="*50)
    
    test_runner = StratificationTestRunner()
    
    # 测试钻孔柱状图数据提取
    result1 = await test_runner.test_drill_bar_chart_extraction()
    print(f"钻孔柱状图提取: {'✓ 成功' if result1['success'] else '✗ 失败'}")
    print(f"  提取的数据点: {result1['data_points_count']}")
    
    # 测试静力触探测试成果图表数据提取
    result2 = await test_runner.test_static_penetration_chart_extraction()
    print(f"静力触探测试成果图表提取: {'✓ 成功' if result2['success'] else '✗ 失败'}")
    print(f"  提取的数据点: {result2['data_points_count']}")
    
    # 测试静力触探分层参数表数据提取
    result3 = await test_runner.test_static_penetration_param_extraction()
    print(f"静力触探分层参数表提取: {'✓ 成功' if result3['success'] else '✗ 失败'}")
    print(f"  提取的数据点: {result3['data_points_count']}")
    
    return result1['success'] and result2['success'] and result3['success']


async def test_data_comparison():
    """测试数据对比功能"""
    print("\n" + "="*50)
    print("测试数据对比功能")
    print("="*50)
    
    test_runner = StratificationTestRunner()
    result = await test_runner.test_data_comparison()
    
    print(f"数据对比测试: {'✓ 成功' if result['success'] else '✗ 失败'}")
    print(f"  对比项数量: {result['comparisons_count']}")
    print(f"  不一致项数量: {result['inconsistencies_count']}")
    print(f"  一致性率: {result['consistency_rate']}%")
    
    return result['success']


def test_visualization():
    """测试可视化功能"""
    print("\n" + "="*50)
    print("测试可视化功能")
    print("="*50)
    
    visualizer = StratificationTestVisualizer()
    
    # 创建并可视化三种测试数据
    test_cases = [
        ("钻孔柱状图", StratificationTestData.create_drill_bar_chart_test_data()),
        ("静力触探测试成果图表", StratificationTestData.create_static_penetration_chart_test_data()),
        ("静力触探分层参数表", StratificationTestData.create_static_penetration_param_test_data())
    ]
    
    saved_files = []
    for title, test_data in test_cases:
        try:
            output_path = visualizer.visualize_test_data(test_data, title)
            saved_files.append(output_path)
            print(f"✓ {title} 可视化图片已保存: {output_path}")
        except Exception as e:
            print(f"✗ {title} 可视化失败: {e}")
            return False
    
    print(f"\n共生成 {len(saved_files)} 个可视化图片")
    return len(saved_files) == 3


async def run_comprehensive_test():
    """运行综合测试"""
    print("开始分层信息审查功能综合测试")
    print("="*60)
    
    test_results = []
    
    # 1. 测试钻孔号提取
    try:
        result1 = await test_drill_id_extraction()
        test_results.append(("钻孔号提取", result1))
    except Exception as e:
        print(f"钻孔号提取测试异常: {e}")
        test_results.append(("钻孔号提取", False))
    
    # 2. 测试表格类型识别
    try:
        result2 = await test_table_type_identification()
        test_results.append(("表格类型识别", result2))
    except Exception as e:
        print(f"表格类型识别测试异常: {e}")
        test_results.append(("表格类型识别", False))
    
    # 3. 测试数据提取
    try:
        result3 = await test_data_extraction()
        test_results.append(("数据提取", result3))
    except Exception as e:
        print(f"数据提取测试异常: {e}")
        test_results.append(("数据提取", False))
    
    # 4. 测试数据对比
    try:
        result4 = await test_data_comparison()
        test_results.append(("数据对比", result4))
    except Exception as e:
        print(f"数据对比测试异常: {e}")
        test_results.append(("数据对比", False))
    
    # 5. 测试可视化
    try:
        result5 = test_visualization()
        test_results.append(("可视化", result5))
    except Exception as e:
        print(f"可视化测试异常: {e}")
        test_results.append(("可视化", False))
    
    # 输出测试结果汇总
    print("\n" + "="*60)
    print("测试结果汇总")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 项测试通过")
    print(f"通过率: {passed/total*100:.1f}%")
    
    return passed == total


if __name__ == "__main__":
    # 运行综合测试
    success = asyncio.run(run_comprehensive_test())
    
    if success:
        print("\n🎉 所有测试通过！分层信息审查功能实现正确。")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，请检查实现。")
        sys.exit(1)
